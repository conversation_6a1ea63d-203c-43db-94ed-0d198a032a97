FROM cr.ttyuyin.com/public/python:3.11

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /usr/app

# 确保 .env 被添加
ADD .env .
ADD . .

RUN pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple &&  \
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

ADD start.sh .
RUN chmod +x start.sh

CMD ["./start.sh"]
