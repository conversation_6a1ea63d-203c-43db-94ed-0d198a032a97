CREATE TABLE knowledgebase (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    query_index VARCHAR NOT NULL,
    file_name VARCHAR NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT knowledgebase_user_id_idx UNIQUE (user_id),
    CONSTRAINT knowledgebase_file_name_idx UNIQUE (file_name)
);

CREATE TABLE common_knowledgebase (
    id SERIAL PRIMARY KEY,
    source_type VARCHAR(32) NOT NULL,
    query_index VARCHAR(128) NOT NULL,
    space_id VARCHAR(128),
    description TEXT,
    dir_token VARCHAR(128),
    space_prefix VARCHAR(128),
    create_time TIMESTAMP
);


CREATE TABLE knowledgebase_file_info (
    id SERIAL PRIMARY KEY,
    parent_id INT NOT NULL,
    token VARCHAR NOT NULL,
    update_time INT DEFAULT 0,
    source VARCHAR NOT NULL
);

-- 创建索引
CREATE INDEX knowledgebase_file_info_parent_id_idx ON knowledgebase_file_info (parent_id);
CREATE INDEX knowledgebase_file_info_token_idx ON knowledgebase_file_info (token);
CREATE INDEX knowledgebase_file_info_source_idx ON knowledgebase_file_info (source);