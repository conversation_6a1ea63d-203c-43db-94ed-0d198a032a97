from langchain.text_splitter import MarkdownTextSplitter
from typing import List


class ImprovedMarkdownSplitter:
    def __init__(self, chunk_size=4096, chunk_overlap=50, min_chunk_size=100):
        self.splitter = MarkdownTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
        )
        self.min_chunk_size = min_chunk_size

    def split_text(self, text: str) -> List[str]:
        # Get initial chunks
        chunks = self.splitter.split_text(text)

        # Process chunks to handle small ones
        processed_chunks = []
        buffer = ""

        for chunk in chunks:
            # If chunk is too small
            if len(chunk) < self.min_chunk_size:
                # If we have a buffer, append this small chunk to it
                if buffer:
                    buffer += "\n\n" + chunk
                else:
                    buffer = chunk
            else:
                # If we have a buffer, append it to the current chunk and add to results
                if buffer:
                    processed_chunks.append(buffer + "\n\n" + chunk)
                    buffer = ""
                else:
                    processed_chunks.append(chunk)

        # Don't forget any remaining buffer
        if buffer:
            # If the last buffer is still too small, append to the last chunk if possible
            if processed_chunks and len(buffer) < self.min_chunk_size:
                processed_chunks[-1] += "\n\n" + buffer
            else:
                processed_chunks.append(buffer)

        return processed_chunks


# Example usage
text = """# Main Title
Some content here...

# 子页面目录

## Section 1
Content for section 1...

## Section 2
Content for section 2...
"""

splitter = ImprovedMarkdownSplitter(chunk_size=4096, chunk_overlap=50, min_chunk_size=4096)
chunks = splitter.split_text(text)

print(f"Number of chunks: {len(chunks)}")
for i, chunk in enumerate(chunks):
    print(f"\nChunk {i + 1} (length: {len(chunk)}):")
    print(f"{chunk[:100]}...")
