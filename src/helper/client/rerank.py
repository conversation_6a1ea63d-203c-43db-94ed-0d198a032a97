from typing import List

import requests

from settings import get_or_creat_settings_ins


def rerank(query: str, texts: List[str]):
    config = get_or_creat_settings_ins()
    data = {
        "query": query,
        "raw_scores": False,
        "return_text": False,
        "texts": texts,
        "truncate": False,
        "truncation_direction": "Right"
    }

    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }

    response = requests.post(config.rerank.url + '/rerank', json=data, headers=headers)
    return response.json()
