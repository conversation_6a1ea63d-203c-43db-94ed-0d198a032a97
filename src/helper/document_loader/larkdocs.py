import json
import logging
from typing import Any, Dict, Iterator, List, Optional

import lark_oapi
import requests
import tenacity
from langchain_community.document_loaders.base import BaseLoader
from langchain_core.documents import Document

from lark_oapi.api.docx.v1 import (
    Block,
    ListDocumentBlockRequest,
    ListDocumentBlockResponse,
    ListDocumentBlockResponseBody,
)
from lark_oapi.core.token.manager import TokenManager

from src.helper.lark2md.doc import DocParser
from src.helper.lark2md.docx import DocxParser

logger = logging.getLogger(__name__)

open_feishu = "https://open.feishu.cn"


class LarkMDLoader(BaseLoader):
    def __init__(
            self,
            client: lark_oapi.Client,
            source: str,
            document_id: str,
            title: str,
            doc_type: str,
    ):
        self.document_id = document_id
        self.title = title
        self.doc_type = doc_type
        self.source = source
        self._client = client
        if doc_type == "doc":
            data = self._request_doc_blocks()
            self.client = DocParser(document_id=document_id, origin_data=data)
        elif doc_type == "docx":
            data = self._request_docx_blocks()
            self.client = DocxParser(document_id=document_id, blocks=data)  # type: ignore
        else:
            raise ValueError("doc_type must be doc or docx")

    def lazy_load(self) -> Iterator[Document]:
        meta = {"source": self.source, "title": self.title}
        content = self.client.parse()
        if content is not None:
            yield Document(page_content=content, metadata=meta)

    def load(self) -> list[Document]:
        return list(self.lazy_load())

    @tenacity.retry(stop=tenacity.stop_after_attempt(3), wait=tenacity.wait_fixed(10))
    def _request_doc_blocks(self) -> Dict[str, Any]:

        token = TokenManager.get_self_tenant_token(self._client._config)  # type: ignore
        path = f"{open_feishu}/open-apis/doc/v2/{self.document_id}/content"
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get(path, headers=headers, timeout=60)
        return json.loads(resp.json()["data"]["content"])

    @tenacity.retry(stop=tenacity.stop_after_attempt(3), wait=tenacity.wait_fixed(10))
    def _request_docx_blocks(self) -> List[Block]: \
 \
            # 构造请求对象
            def handler(
                    page_token: str, document_id: str
            ) -> Optional[ListDocumentBlockResponseBody]:
                request: ListDocumentBlockRequest = (
                    ListDocumentBlockRequest.builder()
                    .document_id(document_id)
                    .page_size(500)
                    .document_revision_id(-1)
                    .page_token(page_token)
                    .build()
                )

                # 发起请求
                option = lark_oapi.RequestOption.builder().build()
                response: ListDocumentBlockResponse = (
                    self._client.docx.v1.document_block.list(request, option)  # type: ignore
                )

                # 处理失败返回
                if not response.success():
                    lark_oapi.logger.error(
                        f"client.docx.v1.document_block.list failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                    )
                    raise Exception(
                        f"client.docx.v1.document_block.list failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                    )
                return response.data

            bodies: List[Block] = []
            page_token = ""
            while True:
                body = handler(page_token=page_token, document_id=self.document_id)  # type: ignore
                if body is None:
                    break
                bodies.extend(body.items)  # type: ignore
                if body.has_more:
                    page_token = body.page_token
                    continue
                break

            return bodies
