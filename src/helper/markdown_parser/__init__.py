class MarkdownNode:
    def __init__(self, title, level):
        self.title = title
        self.level = level
        self.content = ""

    def to_dict(self):
        return {
            "title": self.title,
            "content": self.content.strip()
        }


def parse_markdown_first_level(markdown_text):
    lines = markdown_text.splitlines()

    # 找到所有第一级标题的位置
    first_level_positions = []
    for i, line in enumerate(lines):
        stripped_line = line.strip()
        if stripped_line.startswith("#") and not stripped_line.startswith("##"):
            # 确保是第一级标题（只有一个#）
            level = len(stripped_line) - len(stripped_line.lstrip('#'))
            if level == 1:
                title = stripped_line.lstrip('#').strip()
                first_level_positions.append((i, title))

    # 为每个第一级标题收集其完整内容
    first_level_nodes = []

    for idx, (line_idx, title) in enumerate(first_level_positions):
        # 找到下一个第一级标题的位置，确定内容范围
        if idx + 1 < len(first_level_positions):
            end_idx = first_level_positions[idx + 1][0]
        else:
            end_idx = len(lines)

        # 收集内容（包含标题本身和其下的所有内容）
        content_lines = []
        # 添加标题行本身
        content_lines.append(lines[line_idx])
        # 添加标题下的内容
        for i in range(line_idx + 1, end_idx):
            content_lines.append(lines[i])

        # 创建节点
        node = MarkdownNode(title, 1)
        node.content = "\n".join(content_lines)
        first_level_nodes.append(node)

    return first_level_nodes


if __name__ == "__main__":
    sample_markdown = """# 一、前言

-


- 垂直列表筛选有2种下发服务：走推荐词表的新版筛选，只能筛选发布条件的旧版筛选，移动端这两种筛选面板UI有区别


- 本次PC端，筛选下发服务保留2套，但是前端样式尽可能保持一致


- **本文档筛选统一口径：词表筛选=走推荐词表下发筛选，旧版筛选=只能筛发布条件**


# 二、快筛区域

- [Figma](Figma)
	![](HbI5bTONpoAGfMxANdecH5zan4g)

#### 2.0 相同部分

- **最大展示个数：**所有的快筛区域最多展示X个标签（X以屏幕为准）


- **标签勾选状态：**
	- 【快筛区域】标签勾选状态要跟**筛选面板 **里选项保持一致

	- 【快筛区域】勾选上后，【筛选入口】展已筛个数


#### 2.1 词表筛选特殊化

- **标签排序：**
	- 若用户没有筛选历史：按照热门标签配置顺序

	- 若用户有筛选历史：筛选历史＞热门标签（去重展示）


#### 2.2 旧版筛选特殊化

- **标签排序：**
	- 若用户没有筛选历史：按照筛选面板字段顺序，从上往下排列


# 三、筛选面板

![](Y9q1bugnCo7rOXxfD6Wc4FESnNc)

#### 3.0 相同部分

- **面板主标题：**筛选


- **标签勾选上限**：25个，可多选，超过上限触发提示：已经选了很多了


#### 3.1 词表筛选特殊化

- **搜索框交互描述**
	- 搜索输入框文案：有搜索引导词时展示搜索引导词，没有时默认文案是"搜索玩法"


#### 3.2 旧版筛选特殊化
###### aaa
######## bbbbbb
cccccc内容
####### ccccc
######## dd
- **用户选择后匹配规则：**
	- 不走词表筛选只能筛选发布条件

# 四、扩列筛选特别说明

- **PC端扩列发布字段**配置 跟移动端 是2套配置，因此需要 做出以下逻辑调整
	- 若扩列**走词表筛选：**发布勾选了玩法筛选+热门 展示筛选字段

"""

    print("=== 第一级标题解析结果 ===")
    nodes = parse_markdown_first_level(sample_markdown)
    print(nodes)
