from src.core.rag.doa import operator
from src.helper.document_filter.filter import DocFilter


class DBFilter(DocFilter):
    def add(self, parent_id: int, node_token: str, update_ts: int, source: str) -> None:
        operator.update_or_create_file_info(parent_id, node_token, update_ts, source)

    def is_update(self, parent_id: int, node_token: str, update_ts: int) -> bool | None:
        f = operator.get_file_info(parent_id, node_token)
        if f is None:
            return None
        if f.update_time < update_ts:
            return True
        return False

    def delete(self, parent_id: int, source: str) -> None:
        operator.delete_file_info(parent_id, source)
