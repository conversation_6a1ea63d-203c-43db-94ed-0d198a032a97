from abc import abstractmethod


class DocFilter:
    @abstractmethod
    def add(self, parent_id: int, node_token: str, update_ts: int, source: str) -> None:
        pass

    @abstractmethod
    def is_update(self, parent_id: int, node_token: str, update_ts: int) -> bool | None:
        pass

    @abstractmethod
    def delete(self, parent_id: int, node_token: str) -> None:
        pass


class DummyFilter(DocFilter):
    def add(self, parent_id: int, node_token: str, update_ts: int, source: str) -> None:
        pass

    def is_update(self, parent_id: int, node_token: str, update_ts: int) -> bool | None:
        return None

    def delete(self, parent_id: int, source: str) -> None:
        pass
