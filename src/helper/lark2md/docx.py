from enum import Enum

from lark_oapi.api.docx.v1 import *
from lark_oapi.api.docx.v1.model.block import Block, Text

from .tools.table import MarkdownTable


class Language(Enum):
    PlainText = 1
    ABAP = 2
    Ada = 3
    Apache = 4
    Apex = 5
    Assembly = 6
    Bash = 7
    CSharp = 8
    Cpp = 9
    C = 10
    COBOL = 11
    CSS = 12
    CoffeeScript = 13
    D = 14
    Dart = 15
    Delphi = 16
    Django = 17
    Dockerfile = 18
    Erlang = 19
    Fortran = 20
    FoxPro = 21
    Go = 22
    Groovy = 23
    HTML = 24
    HTMLBars = 25
    HTTP = 26
    Haskell = 27
    JSON = 28
    Java = 29
    JavaScript = 30
    Julia = 31
    Kotlin = 32
    LateX = 33
    Lisp = 34
    Logo = 35
    Lua = 36
    MATLAB = 37
    Makefile = 38
    Markdown = 39
    Nginx = 40
    Objective = 41
    OpenEdgeABL = 42
    PHP = 43
    Perl = 44
    PostScript = 45
    Power = 46
    Prolog = 47
    ProtoBuf = 48
    Python = 49
    R = 50
    RPG = 51
    Ruby = 52
    Rust = 53
    SAS = 54
    SCSS = 55
    SQL = 56
    Scala = 57
    Scheme = 58
    Scratch = 59
    Shell = 60
    Swift = 61
    Thrift = 62
    TypeScript = 63
    VBScript = 64
    Visual = 65
    XML = 66
    YAML = 67
    CMake = 68
    Diff = 69
    Gherkin = 70
    GraphQL = 71
    OpenGLShadingLanguage = 72
    Properties = 73
    Solidity = 74
    TOML = 75

    @classmethod
    def to_name(cls, num: int) -> str:
        return cls(num).name.lower()


class BlockType(Enum):
    PAGE = 1
    TEXT = 2
    HEADING1 = 3
    HEADING2 = 4
    HEADING3 = 5
    HEADING4 = 6
    HEADING5 = 7
    HEADING6 = 8
    HEADING7 = 9
    HEADING8 = 10
    HEADING9 = 11
    BULLET = 12
    ORDERED = 13
    CODE = 14
    QUOTE = 15
    TODO = 17
    BITABLE = 18
    CALLOUT = 19
    CHAT_CARD = 20
    DIAGRAM = 21
    DIVIDER = 22
    FILE = 23
    GRID = 24
    GRID_COLUMN = 25
    IFRAME = 26
    IMAGE = 27
    ISV = 28
    MINDNOTE = 29
    SHEET = 30
    TABLE = 31
    TABLE_CELL = 32
    VIEW = 33
    QUOTE_CONTAINER = 34
    TASK = 35
    OKR = 36
    OKR_OBJECTIVE = 37
    OKR_KEY_RESULT = 38
    OKR_PROGRESS = 39
    ADD_ONS = 40
    JIRA_ISSUE = 41
    WIKI_CATALOG = 42
    BOARD = 43
    UNDEFINED = 999


class DocxParser():
    def __init__(self, document_id: str, blocks: List[Block]):
        self.document_id = document_id
        self.block_map = {i.block_id: i for i in blocks}
        self.blocks = blocks
        self.in_tabls = set()

    def _parse_text(self, t: Text) -> str:

        def _handle_element_style(text_run: TextRun) -> str:
            text = text_run.content
            element_style = text_run.text_element_style

            if element_style.bold:
                text = f"**{text}**"
                return text
            if element_style.italic:
                text = f"*{text}*"
                return text
            if element_style.strikethrough:
                text = f"~~{text}~~"
                return text
            if element_style.underline:
                text = f"<u>{text}</u>"
                return text
            if element_style.inline_code:
                text = f"`{text}`"
                return text
            if element_style.link:
                text = f"[{text}]({text})"
                return text
            return text

        s = ""
        for i in t.elements:
            if i.text_run:
                s += _handle_element_style(i.text_run)
        return s

    def _parse_text_block(self, b: Block) -> str:
        s =  self._parse_text(b.text) + "\n"
        if  b.children is None:
            return s
        # s += "\n"
        for child in b.children:
            block = self.block_map[child]
            cell = self._parse_block(block)
            s += f"{cell}\n"
        return s

    def _parse_header_block(self, b: Block, level: int) -> str:
        text = getattr(b, f"heading{level}")
        header = "#" * level + " " + self._parse_text(text)
        return header + "\n"

    def _parse_callout_block(self, b: Block) -> str:
        s = ""
        for child in b.children:
            block = self.block_map[child]
            cell = self._parse_block(block)
            s += f"{cell}\n"
        return s

    def _parse_ordered_block(self, b: Block, indent: int) -> str:

        order = 1
        parent = self.block_map[b.parent_id]
        if parent.children:
            for idx, child in enumerate(parent.children):
                if b.block_id == child:
                    for i in range(idx - 1, -1, -1):
                        if self.block_map[parent.children[i]].block_type == BlockType.ORDERED.value:
                            order += 1
                        else:
                            break
                    break
        s = f"{order}.{self._parse_text(b.ordered)}" + "\n"
        if not b.children:
            return s + "\n"
        for child in b.children:
            block = self.block_map[child]
            s = s + "\n" + self._parse_block(block, indent + 1)
        return s

    def _parse_bullet_block(self, b: Block, indent) -> str:
        s = f"- {self._parse_text(b.bullet)}\n"
        if not b.children:
            return s + "\n"
        for child in b.children:
            block = self.block_map[child]
            s += self._parse_block(block, indent + 1)
        return s

    def _parse_image(self, b: Block) -> str:
        return f"![]({b.image.token})" + "\n"

    def _parse_sheet(self, b: Block) -> str:
        return f"<sheet>{b.sheet.token}</sheet>" + "\n"

    def _parse_table_cell(self, children: List[str]) -> str:
        s = ""
        for child in children:
            block = self.block_map[child]
            cell = self._parse_block(block)
            s += cell

        return s

    def _parse_table(self, b: Block) -> str:
        rows = []

        for i, tb in enumerate(b.table.cells):
            # print(tb)
            block = self.block_map[tb]
            # print(block.quote)
            cell = self._parse_table_cell(block.children)
            # print(cell)
            rowIndex = int(i / b.table.property.column_size)
            if len(rows) < rowIndex + 1:
                rows.append([])
            rows[rowIndex].append(cell)
        # print(rows)
        if len(rows) == 1:
            return MarkdownTable(header=[], rows=rows).parse()
        return MarkdownTable(header=rows[0], rows=rows[1:]).parse()

    def _parse_quote_container(self, b: Block) -> str:
        s = ""
        for child in b.children:
            block = self.block_map[child]
            cell = self._parse_block(block) + "\n"
            s += cell
        # print(s)
        return f"> {s}"

    def _parse_grid_column(self, b: Block) -> str:
        s = ""
        for child in b.children:
            block = self.block_map[child]
            s += self._parse_block(block)
        return s

    def _parse_grid_block(self, b: Block) -> str:
        s = ""
        for child in b.children:
            col_block = self.block_map[child]
            for col_child in col_block.children:
                block = self.block_map[col_child]
                s += self._parse_block(block)
        return s

    def _parse_todo_block(self, b: Block, indent: int = 0) -> str:
        s = self._parse_text(b.todo)
        if b.todo.style.done:
            s = f"- [x] {s}\n"
        else:
            s = f"- [] {s}\n"
        if not b.children:
            return s + "\n"

        for child in b.children:
            block = self.block_map[child]
            s += self._parse_block(block, indent + 1)
        return s

    def _parse_code_block(self, b: Block) -> str:
        if b.code.style.language is None:
            return f"```\n{self._parse_text(b.code)}\n```"
        lang = Language.to_name(b.code.style.language)
        return f"``` {lang}\n{self._parse_text(b.code)}\n```"

    def _parse_block(self, block: Block, indentLevel: int = 0) -> str:
        prefix = "\t" * indentLevel
        if block.block_id in self.in_tabls:
            return ""
        self.in_tabls.add(block.block_id)
        if block.block_type == BlockType.TEXT.value:
            return prefix + self._parse_text_block(block)
        elif block.block_type == BlockType.HEADING1.value:
            return prefix + self._parse_header_block(block, 1)
        elif block.block_type == BlockType.HEADING2.value:
            return prefix + self._parse_header_block(block, 2)
        elif block.block_type == BlockType.HEADING3.value:
            return prefix + self._parse_header_block(block, 3)
        elif block.block_type == BlockType.HEADING4.value:
            return prefix + self._parse_header_block(block, 4)
        elif block.block_type == BlockType.HEADING5.value:
            return prefix + self._parse_header_block(block, 5)
        elif block.block_type == BlockType.HEADING6.value:
            return prefix + self._parse_header_block(block, 6)
        elif block.block_type == BlockType.HEADING7.value:
            return prefix + self._parse_header_block(block, 7)
        elif block.block_type == BlockType.HEADING8.value:
            return prefix + self._parse_header_block(block, 8)
        elif block.block_type == BlockType.HEADING9.value:
            return prefix + self._parse_header_block(block, 9)
        elif block.block_type == BlockType.BULLET.value:
            return prefix + self._parse_bullet_block(block, indentLevel)
        elif block.block_type == BlockType.ORDERED.value:
            return prefix + self._parse_ordered_block(block, indentLevel)
        elif block.block_type == BlockType.CODE.value:
            return prefix + self._parse_code_block(block)
        elif block.block_type == BlockType.IMAGE.value:
            return prefix + self._parse_image(block)
        elif block.block_type == BlockType.CALLOUT.value:
            return prefix + self._parse_callout_block(block)
        elif block.block_type == BlockType.TABLE.value:
            return self._parse_table(block)
        elif block.block_type == BlockType.QUOTE_CONTAINER.value:
            return prefix + self._parse_quote_container(block)
        elif block.block_type == BlockType.GRID.value:
            return prefix + self._parse_grid_block(block)
        elif block.block_type == BlockType.GRID_COLUMN.value:
            return prefix + self._parse_grid_column(block)
        elif block.block_type == BlockType.TODO.value:
            return prefix + self._parse_todo_block(block, indentLevel)
        elif block.block_type == BlockType.DIVIDER.value:
            return "---\n"
        elif block.block_type == BlockType.SHEET.value:
            return prefix + self._parse_sheet(block)
        else:
            return ""

    def parse(self):
        s = ""
        for b in self.blocks:
            # print(b.block_type, " ", b.block_id)
            content = self._parse_block(b)
            if content:
                s += f"{content}\n"
        return s
