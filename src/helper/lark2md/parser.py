import json

from doc import DocParser
from docx import DocxParser
from lark_oapi.api.docx.v1 import *
from lark_oapi.core.token import TokenManager
from tools.lark import *

DocumentType = str


class LarkDocumentParser:

    OPENAI_FEISHU = "https://open.feishu.cn"

    def __init__(
        self,
        feishu_url: str,
        lark_client: lark.Client = None,
        lark_auth_config: LarkAuthConfig = None,
    ):
        self.feishu_url = feishu_url
        self.document_id: Optional[str] = None
        self.document_type: Optional[DocumentType] = None
        self.document_name: Optional[str] = None
        self._doc_parser: Optional[DocParser] = None
        self._docx_parser: Optional[DocxParser] = None
        if lark_client is not None:
            self._client = lark_client
        elif lark_auth_config is not None:
            self._client = self._init_default_lark_cli(lark_auth_config)
        else:
            raise Exception("lark_client or lark_auth_config must be not None")

        self._parse_wiki_document()

    def _init_default_lark_cli(self, lark_auth_config: LarkAuthConfig) -> lark.Client:
        return (
            lark.Client.builder()
            .app_id(lark_auth_config.app_id)
            .app_secret(lark_auth_config.app_secret)
            .build()
        )

    def _parse_wiki_document(self):
        pattern = (
            r"https:\/\/(?:[a-zA-Z0-9\-]+\.)+feishu\.cn\/(wiki|docx|docs)\/([^\/]+)"
        )
        match = re.match(pattern, self.feishu_url)
        if match:
            tmp_doc_id = match.group(2)
            dc_type = match.group(1)

            if "wiki" in dc_type:
                request: GetNodeSpaceRequest = (
                    GetNodeSpaceRequest.builder().token(tmp_doc_id).build()
                )
                response: GetNodeSpaceResponse = self._client.wiki.v2.space.get_node(
                    request
                )

                if not response.success():
                    raise Exception(
                        f"client.docx.v1.document.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                    )

                if response.data is None:
                    raise Exception(
                        "Response data is none. Perhaps there is no permission for that document."
                    )

                self.document_type = response.data.node.obj_type
                self.document_id = response.data.node.obj_token
                self.document_name = response.data.node.title
            else:
                self.document_type = dc_type
                self.document_id = tmp_doc_id
                request: GetDocumentRequest = (
                    GetDocumentRequest.builder().document_id(tmp_doc_id).build()
                )
                response: GetDocumentResponse = self._client.docx.v1.document.get(
                    request
                )
                if not response.success():
                    raise Exception(
                        f"client.docx.v1.document.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                    )

                if response.data is None:
                    raise Exception(
                        "Response data is none. Perhaps there is no permission for that document."
                    )

                self.document_name = response.data.document.title

            return
        raise Exception("invalid feishu url")

    def _request_doc_blocks(self) -> Dict[str, Any]:

        token = TokenManager.get_self_tenant_token(self._client._config)  # type: ignore
        # path = f"{open_feishu}/open-apis/doc/v2/doccnyVhYsvqOpEh4B7Xjp8LClh/raw_content"
        path = f"{self.OPENAI_FEISHU}/open-apis/doc/v2/{self.document_id}/content"
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get(path, headers=headers)
        return json.loads(resp.json()["data"]["content"])

    def _request_docx_blocks(self) -> List[Block]:
        """
        获取文档块列表
        :param client:
        :param page_token:
        :param document_id:
        :return:
        """

        # 构造请求对象
        def handler(
            page_token: str, document_id: str
        ) -> Optional[ListDocumentBlockResponseBody]:
            request: ListDocumentBlockRequest = (
                ListDocumentBlockRequest.builder()
                .document_id(document_id)
                .page_size(500)
                .document_revision_id(-1)
                .page_token(page_token)
                .build()
            )

            # 发起请求
            option = lark.RequestOption.builder().build()
            response: ListDocumentBlockResponse = (
                self._client.docx.v1.document_block.list(request, option)
            )

            # 处理失败返回
            if not response.success():
                lark.logger.error(
                    f"client.docx.v1.document_block.list failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
                )
                return None
            return response.data

        bodies: List[Block] = []
        page_token = ""
        while True:
            body = handler(page_token=page_token, document_id=self.document_id)
            if body is None:
                break
            bodies.extend(body.items)
            if body.has_more:
                page_token = body.page_token
                continue
            break
        return bodies

    def parse(self) -> str:
        if self.document_type == RT_DOC or self.document_type == RT_DOCS:
            self._doc_parser = DocParser(self.document_id, self._request_doc_blocks())
            return self._doc_parser.parse()
        elif self.document_type == RT_DOCX:
            self._docx_parser = DocxParser(
                self.document_id, self._request_docx_blocks()
            )
            return self._docx_parser.parse()
        else:
            raise Exception(f"unsupported document type: {self.document_type}")
