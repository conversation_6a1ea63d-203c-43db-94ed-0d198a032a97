import json
from typing import Any, Dict, Optional, List

from pydantic import BaseModel, Field

class Location(BaseModel):
    zoneId: str = Field(default="")
    startIndex: int = Field(default=0)
    endIndex: int = Field(default=0)


class UndefinedBlock(BaseModel):
    """
    未定义块
    """

    def render_to_markdown(self) -> str:
        """
        将文本样式转换为markdown格式
        """
        return ""


class HorizontalLine(BaseModel):

    def render_to_markdown(self) -> str:
        """
        将文本样式转换为markdown格式
        """
        return "---"


class ImageItem(BaseModel):
    fileToken: str = Field(default="")
    width: float = Field(default=0)
    height: float = Field(default=0)


class Gallery(BaseModel):
    imageList: List[ImageItem] = Field(default=[])

    def render_to_markdown(self) -> str:
        """
        将文本样式转换为markdown格式
        """
        return "\n".join([f"![]({i.fileToken})" for i in self.imageList])


class Link(BaseModel):
    """
    超链接
    """
    url: str = Field(default="")

    def render_to_markdown(self, text: str = None) -> str:
        """
        将文本样式转换为markdown格式
        """
        if text is None:
            text = self.url
        return f"[{text}]({self.url})"


class Person(BaseModel):
    """
    人员
    """
    openId: str = Field(default="")

    def render_to_markdown(self, text: str = None) -> str:
        """
        将文本样式转换为markdown格式
        """
        if text is None:
            text = "openId"
        return f"[{text}]({self.openId})"


class DocsLink(Link):
    """
    文档链接
    """
    url: str = Field(default="")

    def render_to_markdown(self, text: str = None) -> str:
        """
        将文本样式转换为markdown格式
        """
        if text is None:
            text = self.url
        return f"[{text}]({self.url})"


class TextStyle(BaseModel):
    """
    文本样式
    """
    bold: bool = Field(default=False)
    italic: bool = Field(default=False)
    strikeThrough: bool = Field(default=False)
    codeInline: bool = Field(default=False)
    link: Optional[Link] = None


class TextRun(BaseModel):
    """
    文本段落
    """
    text: str
    style: Optional[TextStyle] = None

    def render_to_markdown(self) -> str:
        """
        将文本样式转换为markdown格式
        """
        content = self.text
        if self.style:
            if self.style.bold:
                content = "**" + content + "**"
            if self.style.italic:
                content = "*" + content + "*"
            if self.style.strikeThrough:
                content = "~~" + content + "~~"
            if self.style.codeInline:
                content = "`" + content + "`"

            if self.style.link:
                content = self.style.link.render_to_markdown(content)

        return content


class ListStyle(BaseModel):

    type: str = Field(default="")
    indentLevel: int = Field(default=0)
    number: int = Field(default=0)


class ParagraphStyle(BaseModel):
    """
    段落样式
    """
    headingLevel: int = Field(default=0)
    list: Optional[ListStyle] = None
    quote: bool = Field(default=False)

    def render_to_markdown(self, content: str) -> str:
        """
        将段落样式转换为markdown格式
        """
        if self.list:
            if self.list.type == "bullet":
                content = "\t" * (self.list.indentLevel - 1) + "- " + content
            elif self.list.type == "number":
                content = "\t" * (self.list.indentLevel - 1) + \
                    str(self.list.number) + ". " + content
            elif self.list.type == "checkBox":
                content = "\t" * (self.list.indentLevel -
                                  1) + "- [] " + content
            elif self.list.type == "checkedBox":
                content = "\t" * (self.list.indentLevel -
                                  1) + "- [x] " + content

        if self.headingLevel:
            content = "#" * self.headingLevel + " " + content

        if self.quote:
            content = "> " + content

        return content


class ParagraphElement(BaseModel):
    """
    段落元素
    """
    type: str
    textRun: Optional[TextRun] = None
    docsLink: Optional[DocsLink] = None
    person: Optional[Person] = None

    def render_to_markdown(self) -> str:
        """
        将文本段落转换为markdown格式
        """
        if self.type == "textRun":
            return self.textRun.render_to_markdown()

        if self.type == "person":
            return self.person.render_to_markdown()

        if self.type == "docsLink":
            if self.textRun is None:
                return self.docsLink.render_to_markdown()
            return self.docsLink.render_to_markdown(self.textRun.text)

        return "None"


class Paragraph(BaseModel):
    """
    段落
    """
    style: Optional[ParagraphStyle] = None
    elements: List[ParagraphElement] = Field(default=[])
    # location: Optional[Location] = Field()


class TableCell(BaseModel):
    """
    表格单元格
    """
    columnIndex: int = Field(default=0)
    zoneId: str = Field(default="")
    body: "Body" = None

    def render_table_cell_to_markdown(self) -> str:
        res = ""
        if self.body is None:
            return res
        if self.body.blocks is None:
            return res
        for b in self.body.blocks:
            if b.type == "paragraph":
                last_idx = len(self.body.blocks) - 1
                for idx, elem in enumerate(b.paragraph.elements):
                    if idx == last_idx:
                        res += elem.render_to_markdown()
                    else:
                        res += elem.render_to_markdown() + "<br>"
            if b.type == "gallery":
                res += b.gallery.render_to_markdown()

            if b.type == "code":
                res += b.code.render_to_markdown()
        return res


class TableRow(BaseModel):
    """
    表格行
    """
    rowIndex: int = Field(default=0)
    tableCells: List[TableCell] = Field(default=[])

    def render_to_markdown(self) -> str:
        res = "|"
        for cell in self.tableCells:
            res = res + cell.render_table_cell_to_markdown() + " | "
        return res


class Table(BaseModel):
    """
    表格
    """
    tableId: str = Field(default="")
    tableRows: List[TableRow] = Field(default=[])
    columnSize: int = Field(default=0)
    # location: Optional[Location] = None

    def render_to_markdown(self) -> str:
        res = ""
        header_split = "".join(
            ["| --- " for _ in range(self.columnSize)]) + "|"
        for idx, row in enumerate(self.tableRows):
            res = res + row.render_to_markdown() + "\n"
            if idx == 0:
                res = res + header_split + "\n"
        return res


class Code(BaseModel):
    """
    代码段
    """
    language: str = Field(default="")
    zoneId: str = Field(default="")
    body: Optional["Body"] = None

    def render_to_markdown(self) -> str:
        """
        将代码段转换为markdown格式
        """
        res = "```" + self.language + "\n"
        for b in self.body.blocks:
            if b.type == "paragraph":
                for elem in b.paragraph.elements:
                    res += elem.render_to_markdown() + "\n"
        res += "```"
        return res


class Title(BaseModel):
    elements: Optional[List[ParagraphElement]] = None


class Block(BaseModel):
    """
    文档块
    """
    type: str
    paragraph: Optional[Paragraph] = None
    table: Optional[Table] = None
    code: Optional[Code] = None
    gallery: Optional[Gallery] = None
    horizontalLine: Optional[HorizontalLine] = None
    # undefinedBlock: Optional[UndefinedBlock] = Field()


class Body(BaseModel):
    blocks: Optional[List[Block]] = None


class DocSchema(BaseModel):
    """
    文档原始数据
    """
    title: Optional[Title] = None
    blocks: Optional[List[Block]] = None
    # location: Optional[Location] = Field()



class DocParser:

    def __init__(self, document_id: str, origin_data: Dict[str, Any]):
        print(json.dumps(origin_data, indent=4, ensure_ascii=False))
        self.document_id = document_id
        self.origin_data:  Dict[str, Any] = origin_data
        self._title: Optional[Title] = None
        self._body: Optional[Body] = None
        self._init_origin_data()

    @property
    def title(self) -> str:
        return self._title.elements[0].render_to_markdown()

    def _init_origin_data(self):
        self._title = Title(**self.origin_data["title"])
        self._body = Body(**self.origin_data["body"])

    def _parse_horizontal_line(self, b: Block) -> str:
        if b.horizontalLine:
            return b.horizontalLine.render_to_markdown()

    def _parse_paragraph(self, b: Block) -> str:
        res = ""
        if b.paragraph:
            for item in b.paragraph.elements:
                res += item.render_to_markdown()

        if b.paragraph.style:
            return b.paragraph.style.render_to_markdown(res)
        return res

    def _parse_table(self, b: Block) -> str:

        if b.table:
            return b.table.render_to_markdown()
        return ""

    def _parse_gallery(self, b: Block) -> str:
        res = ""
        if b.gallery:
            return b.gallery.render_to_markdown()
        return res

    def _parse_code(self, b: Block) -> str:
        res = ""
        if b.code:
            res = b.code.render_to_markdown()
        return res

    def _parse_block(self, b: Block) -> str:
        if b.type == "horizontalLine":
            return self._parse_horizontal_line(b)
        elif b.type == "paragraph":
            return self._parse_paragraph(b)
        elif b.type == "table":
            return self._parse_table(b)
        elif b.type == "gallery":
            return self._parse_gallery(b)
        elif b.type == "code":
            return self._parse_code(b)

        else:
            return ""

    def parse(self) -> str:
        res = ""
        for b in self._body.blocks:
            res += self._parse_block(b)
            res += "\n"
        return res
