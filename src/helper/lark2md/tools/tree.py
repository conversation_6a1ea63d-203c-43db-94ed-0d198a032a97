import logging
import random
import re
from typing import List, Optional

from pydantic import BaseModel, Field


class MarkdownNode(BaseModel):
    title: str = Field(default="")
    raw_title: str = Field(default="")
    content: str = Field(default="")
    children: List["MarkdownNode"] = Field(default_factory=list)
    level: int = Field(default=0)
    # 主要用于前端渲染
    id: int = Field(
        default_factory=lambda: random.randint(100, 999) * random.randint(1000, 9999)
    )
    label: str = Field(default="")


class MarkdownTree:
    """
    将 markdown 转换成 树状结构
    """

    def __init__(self, markdown_text: str = None):
        self.markdown_text: Optional[str] = markdown_text
        self.markdown_titles: List[str] = []
        self.root: MarkdownNode = self._parse_markdown()

    def _parse_markdown(self) -> MarkdownNode:
        lines = self.markdown_text.split("\n")
        current = MarkdownNode()
        root = current
        stack: List[MarkdownNode] = [current]

        for line in lines:
            header_match = re.match(r"^(#+)\s*(.*)", line)
            if header_match:
                level = len(header_match.group(1))
                title = header_match.group(2)
                # print(f"Stack: {len(stack)}, Level: {level}, Title: {title}")
                while len(stack) >= level:
                    stack.pop()

                distance = level - len(stack)
                # print(f"Distance: {distance}")
                if distance >= 2:
                    stack.extend(stack[len(stack) - (distance - 1) :])
                raw_title = f"{'#' * level} {title}"
                node = MarkdownNode(
                    title=title,
                    label=title,
                    content=f"{raw_title}\n",
                    children=[],
                    level=level,
                    raw_title=raw_title,
                )
                self.markdown_titles.append(raw_title)
                if stack:
                    parent = stack[-1]
                    parent.children.append(node)
                else:
                    current.children.append(node)
                stack.append(node)
            else:
                if stack:
                    node = stack[-1]
                    node.content += line + "\n"
                else:
                    current.content += line + "\n"

        return root


class SearchMarkdownNode:
    """
    提供两种方法对输进行检索， 一个是节点，另一个是标题匹配；
    """

    def _match_title(self, query_title: str, current_title: str) -> bool:
        if current_title.strip() == "":
            return False

        if query_title.strip() == current_title.strip():
            return True

        pattern = r"^[\d\W]+"
        # 使用 re.sub 函数替换匹配到的部分为空字符串，即删除它们
        real_query_title = re.sub(pattern, "", query_title)
        if real_query_title.strip() in current_title.strip():
            return True

        if current_title.strip() in real_query_title.strip():
            return True

        return False

    def _match_id(self, query_id: int, current_id: int) -> bool:
        if current_id == 0:
            return False
        if query_id == current_id:
            return True
        return False

    def _handle_result(self, md_doc_node: MarkdownNode, result: List[MarkdownNode]):
        if md_doc_node.title != "" or md_doc_node.content != "":
            result.append(md_doc_node)
        if len(md_doc_node.children) == 0:
            return
        for node in md_doc_node.children:
            self._handle_result(node, result)

    def filter_content_with_id(
        self, query_id: int, root: MarkdownNode
    ) -> (List[MarkdownNode], str):
        result: List[MarkdownNode] = []
        if query_id == 0:
            return result, ""

        def handle_recursive(root: MarkdownNode, query_id: int):
            if root.id == query_id:
                if self._match_id(query_id, root.id):
                    logging.info(f"Match: {query_id} ; {root.title}")
                    self._handle_result(root, result)
                    return

            for node in root.children:
                # print(node.title, len(node.children))
                if self._match_id(query_id, node.id):
                    logging.info(f"Match: {query_id} ; {node.title}")
                    self._handle_result(node, result)
                    return
                if len(node.children) != 0:
                    handle_recursive(node, query_id)

        for node in root.children:
            handle_recursive(node, query_id)

        md_text = ""
        for item in result:
            md_text += f"{item.content}\n"

        return result, md_text

    def search_content_with_title(
        self, query_title: str, root: MarkdownNode
    ) -> (List[MarkdownNode], str):
        """
        :param query_title:
        :return: [{"需求名称": "", "需求内容": ""}], <markdown>
        """
        result: List[MarkdownNode] = []
        if query_title.strip() == "":
            return result, ""

        def handle_recursive(root: MarkdownNode, query_title: str):
            if root.title != "":
                if self._match_title(query_title, root.title):
                    logging.info(f"Match: {query_title} ; {root.title}")
                    self._handle_result(root, result)
                    return

            for node in root.children:
                # print(node.title, len(node.children))
                if self._match_title(query_title, node.title):
                    logging.info(f"Match: {query_title} ; {node.title}")
                    self._handle_result(node, result)
                    return
                if len(node.children) != 0:
                    handle_recursive(node, query_title)

        for node in root.children:
            handle_recursive(node, query_title)

        md_text = ""
        for item in result:
            md_text += f"{item.content}\n"

        return result, md_text
