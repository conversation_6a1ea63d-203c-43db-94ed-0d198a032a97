import re
import time
from io import BytesIO
from typing import List, Optional, IO, Any

import lark_oapi as lark
import requests
from lark_oapi.api.docx.v1 import GetDocumentRequest, GetDocumentResponse
from lark_oapi.api.drive.v1 import BatchGetTmpDownloadUrlMediaRequest, BatchGetTmpDownloadUrlMediaResponse, \
    DownloadExportTaskRequest, DownloadExportTaskResponse, CreateExportTaskRequest, ExportTask, \
    CreateExportTaskResponse, GetExportTaskRequest, GetExportTaskResponse
from lark_oapi.api.sheets.v3 import GetSpreadsheetSheetRequest, GetSpreadsheetSheetResponse, \
    GetSpreadsheetSheetResponseBody, QuerySpreadsheetSheetRequest, QuerySpreadsheetSheetResponse, \
    QuerySpreadsheetSheetResponseBody
from lark_oapi.api.wiki.v2 import GetNodeSpaceRequest, GetNodeSpaceResponse
from pydantic import Field, BaseModel

from src.log import logger

lark.logger.setLevel("INFO")

LarkResourceType = str
RT_WIKI: LarkResourceType = "wiki"
RT_DOC: LarkResourceType = "doc"
RT_DOCS: LarkResourceType = "docs"
RT_DOCX: LarkResourceType = "docx"
RT_SHEET: LarkResourceType = "sheet"
RT_BI_TABLE: LarkResourceType = "bitable"

ExportType = str
ET_PDF: ExportType = "pdf"
ET_DOCX: ExportType = "docx"
ET_XLSX: ExportType = "xlsx"
ET_CSV: ExportType = "csv"


class LarkAuthConfig(BaseModel):
    app_id: str
    app_secret: str


class LarkDocumentMeta(BaseModel):
    id_or_token: str = Field(default=None)
    name: str = Field(default=None)
    type: LarkResourceType = Field(default=None)


def init_lark_cli(lark_auth_config: LarkAuthConfig) -> lark.Client:
    return lark.Client.builder().app_id(lark_auth_config.app_id).app_secret(lark_auth_config.app_secret) \
        .build()


def get_document_meta_info(lark_cli: lark.Client, feishu_url: str) -> Optional[LarkDocumentMeta]:
    # 去掉url的参数
    feishu_url = re.sub(r'\?.+', '', feishu_url)
    pattern = r'https:\/\/(?:[a-zA-Z0-9\-]+\.)+feishu\.cn\/(wiki|docx|docs|file|sheets|base)\/([^\/]+)'
    match = re.match(pattern, feishu_url)
    info = LarkDocumentMeta()
    if match:
        tmp_doc_id = match.group(2)
        dc_type = match.group(1)

        if RT_WIKI in dc_type:
            request: GetNodeSpaceRequest = GetNodeSpaceRequest.builder().token(tmp_doc_id).build()
            response: GetNodeSpaceResponse = lark_cli.wiki.v2.space.get_node(
                request)

            if not response.success():
                lark.logger.error(
                    f"get node space failed, {response.code} {response.msg}")

            if response.data is None:
                raise Exception("Response data is none. Perhaps there is no permission for that document.")

            info.id_or_token = response.data.node.obj_token
            info.type = response.data.node.obj_type
            info.name = response.data.node.title

        else:
            info.id_or_token = tmp_doc_id
            info.type = dc_type
            request: GetDocumentRequest = GetDocumentRequest.builder().document_id(tmp_doc_id).build()
            response: GetDocumentResponse = lark_cli.docx.v1.document.get(request)
            if not response.success():
                lark.logger.error(
                    f"client.docx.v1.document.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
                return

            if response.data is None:
                raise Exception("Response data is none. Perhaps there is no permission for that document.")

            info.name = response.data.document.title

    return info


class ImageApi:
    def __init__(self, lark_cli: lark.Client):
        self.lark_cli = lark_cli

    def get_image_url(self, image_tokens: List[str]) -> Optional[BatchGetTmpDownloadUrlMediaResponse]:
        if len(image_tokens) > 5:
            raise Exception("图片数量不能超过 5 张")
        # 构造请求对象
        request: BatchGetTmpDownloadUrlMediaRequest = BatchGetTmpDownloadUrlMediaRequest.builder() \
            .file_tokens(image_tokens) \
            .build()
        # 发起请求
        response: BatchGetTmpDownloadUrlMediaResponse = self.lark_cli.drive.v1.media.batch_get_tmp_download_url(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.drive.v1.media.batch_get_tmp_download_url failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return None

        return response

    def download_image(self, image_url: str) -> BytesIO:
        response = requests.get(image_url)
        if response.status_code != 200:
            raise Exception(f"图片下载失败: {image_url}")
        return BytesIO(response.content)


class exportTask:
    # https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/create

    def __init__(self, lark_cli: lark.Client):
        self.lark_cli = lark_cli
        self.doc_token: str = ""
        self.doc_type: str = ""
        self.export_type: ExportType = ""
        self.sheet_id: str = ""

    def _create_task(self) -> Optional[str]:
        # https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/create

        et = ExportTask.builder().file_extension(self.export_type) \
            .token(self.doc_token) \
            .type(self.doc_type)

        if self.sheet_id is not None:
            et.sub_id(self.sheet_id)
        # 构造请求对象
        request: CreateExportTaskRequest = CreateExportTaskRequest.builder() \
            .request_body(et.build()) \
            .build()

        response: CreateExportTaskResponse = self.lark_cli.drive.v1.export_task.create(request)
        if not response.success():
            lark.logger.error(
                f"client.drive.v1.export_task.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return None


        return response.data.ticket

    def _query_task(self, ticket: str) -> Optional[str]:
        # https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/get

        logger.info(f"client.drive.v1.export_task.get, token: {self.doc_token}, ticket: {ticket}")
        # 构造请求对象
        request: GetExportTaskRequest = GetExportTaskRequest.builder().ticket(ticket) \
            .token(self.doc_token) \
            .build()

        # 最长等待 60s
        count = 0
        while count < 30:
            response: GetExportTaskResponse = self.lark_cli.drive.v1.export_task.get(request)
            if not response.success():
                lark.logger.error(
                    f"client.drive.v1.export_task.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
                return None

            if response.data.result.job_status in [1, 2]:
                time.sleep(2)
                count += 1
                continue

            if response.data.result.job_status == 0:
                return response.data.result.file_token

            raise Exception(f"任务执行失败: [{response.data.result.job_status}] {response.data.result.job_error_msg}")

        return None

    def _download_doc(self, file_token: str) -> Optional[IO[Any]]:
        # https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/download
        request: DownloadExportTaskRequest = DownloadExportTaskRequest.builder() \
            .file_token(file_token) \
            .build()
        response: DownloadExportTaskResponse = self.lark_cli.drive.v1.export_task.download(request)
        if not response.success():
            lark.logger.error(
                f"client.drive.v1.export_task.download failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return None
        return response.file

    def export_doc(self, doc_token: str, doc_type: LarkResourceType, export_type: ExportType, sheet_id: str = None) \
            -> Optional[IO[Any]]:
        if doc_type == RT_SHEET and sheet_id is None:
            raise Exception("Sheet ID is required")

        self.doc_token = doc_token
        self.doc_type = doc_type
        self.export_type = export_type
        self.sheet_id = sheet_id

        uid = f"{self.doc_token}, {self.doc_type}, {self.export_type} ({self.sheet_id})"
        ticket = self._create_task()
        if ticket is None:
            raise Exception(f"创建任务失败, {uid}")

        file_token = self._query_task(ticket)
        if file_token is None:
            raise Exception(f"查询任务失败, {uid}")

        return self._download_doc(file_token)


class SheetApi(exportTask):
    def __init__(self, lark_cli: lark.Client):
        super().__init__(lark_cli)

    def get_sheet_info(self, spreadsheet_token: str, sheet_id: str) -> Optional[GetSpreadsheetSheetResponseBody]:
        # https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet-sheet/get
        # 构造请求对象
        request: GetSpreadsheetSheetRequest = GetSpreadsheetSheetRequest.builder() \
            .spreadsheet_token(spreadsheet_token) \
            .sheet_id(sheet_id) \
            .build()
        # 发起请求
        response: GetSpreadsheetSheetResponse = self.lark_cli.sheets.v3.spreadsheet_sheet.get(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.sheets.v3.spreadsheet_sheet.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return None

        return response.data

    def list_sheets(self, spreadsheet_token: str) -> Optional[QuerySpreadsheetSheetResponseBody]:
        # https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet-sheet/query
        # 构造请求对象
        request: QuerySpreadsheetSheetRequest = QuerySpreadsheetSheetRequest.builder() \
            .spreadsheet_token(spreadsheet_token) \
            .build()

        # 发起请求
        response: QuerySpreadsheetSheetResponse = self.lark_cli.sheets.v3.spreadsheet_sheet.query(request)
        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"client.sheets.v3.spreadsheet_sheet.query failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return None

        return response.data

    def download_sheet(self, spreadsheet_token: str, sheet_id: str, file_extension: ExportType = ET_XLSX) -> Optional[IO[Any]]:
        # https://open.feishu.cn/document/server-docs/docs/drive-v1/export_task/create?

        # token: str, type: str, file_extension: str, sheet_id: str = None
        return self.export_doc(spreadsheet_token, RT_SHEET, file_extension, sheet_id)

# bitable 是飞书的写法
class BitableApi(exportTask):

   def __init__(self, lark_cli: lark.Client):
        super().__init__(lark_cli)





if __name__ == '__main__':
    lark_auth_config = LarkAuthConfig(
        app_id="cli_a1d8686d873a500e",
        app_secret="TEQqjQjYaRqGK4IFc2ADhfYNVhxs58gY"
    )
    lark_client = init_lark_cli(lark_auth_config)


    sheet_url = "https://q9jvw0u5f5.feishu.cn/wiki/A9krwKpoliw7jMkBqjbc5g0an5b"
    bitable_url = "https://q9jvw0u5f5.feishu.cn/wiki/Ke2xwqXGNiVWU5kD0W2cyMvonE7?table=tblA4tJH1LCbLTNI&view=veweA5OLj3"
    meta_info = get_document_meta_info(lark_client, sheet_url)
    print(meta_info)


    # sheet api
    # sheet_api = SheetApi(lark_cli=lark_client)
    # sheet_list = sheet_api.list_sheets(meta_info.id_or_token)
    # print(sheet_list.sheets[0].title, sheet_list.sheets[0].sheet_id)
    # sheet_info = sheet_api.get_sheet_info(meta_info.id_or_token, sheet_list.sheets[0].sheet_id)
    # print(sheet_info.sheet.title)
    # sheet_data = sheet_api.download_sheet(meta_info.id_or_token, sheet_list.sheets[0].sheet_id)
    # with open("sheet.xlsx", "wb") as f:
    #     f.write(sheet_data.read())

    # image
    image_token = "ZfyobXPV9oEXH6xUeCFcq2ZOnAc"
    image_api = ImageApi(lark_cli=lark_client)
    resp = image_api.get_image_url([image_token])
    for url_obj  in resp.data.tmp_download_urls:
        print(url_obj.file_token, url_obj.tmp_download_url)

        _io = image_api.download_image(url_obj.tmp_download_url)

        with open(f"{url_obj.file_token}.png", "wb") as f:
            f.write(_io.read())
