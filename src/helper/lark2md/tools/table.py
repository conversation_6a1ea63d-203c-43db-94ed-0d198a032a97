from typing import List
from pytablewriter import MarkdownTableWriter


class MarkdownTable:

    def __init__(self, header: List[str], rows: List[List[str]]):
        self.header = header
        self.rows = rows
        self._column_count = self._set_column_count()

    def _set_column_count(self) -> int:
        # 先处理头部
        current_max_column_count = len(self.header)
        for row in self.rows:
            if len(row) > current_max_column_count:
                current_max_column_count = len(row)

        return current_max_column_count

    def _fill_uncompleted_row(self, row: List[str]) -> List[str]:
        return row + [""] * (self._column_count - len(row))



    def _replace_n_to_br(self, text: str) -> str:
        # 去掉尾部的换行符
        text = text.rstrip("\n")
        text_list = text.split("\n")
        res = ""
        for t in text_list:
            if t != "":
                t = t.replace("\t", "&nbsp;" * 2)
                res += t + "<br>"
        return res.rstrip("<br>")

    def _make_header(self) -> List[str]:
        header = []
        self.header = self._fill_uncompleted_row(self.header)
        for h in self.header:
            h = self._replace_n_to_br(h)
            header.append(h)
        return header

    def parse(self):
        lines = []
        for row in self.rows:
            _row = []
            for t in self._fill_uncompleted_row(row):
                _row.append(self._replace_n_to_br(t))
            lines.append(_row)
        return MarkdownTableWriter(headers=self._make_header(), value_matrix=lines).dumps()

