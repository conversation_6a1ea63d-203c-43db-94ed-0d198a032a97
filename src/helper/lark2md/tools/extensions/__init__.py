import re
from typing import List, Dict, Any

from langchain_core.messages import HumanMessage

from .excel import SheetItem, SheetParser
from .image import ImageItem, ImageParser


class ExtensionPromptMessages:

    def __init__(self,
                 md: str,
                 sheet_items: List[SheetItem] = None,
                 image_items: List[ImageItem] = None,
                 ):
        self.md = md
        self.sheet_items = sheet_items if sheet_items is not None else []
        self.image_items = image_items if image_items is not None else []
        self._replace_table_tag()
    #
    #
    def _replace_table_tag(self):
        # sheet
        self.md  = SheetParser.replace_html_into_markdown(self.md, self.sheet_items)

        # bitable


    def _is_need_image_prompt(self) -> bool:
        if  len(self.image_items) != 0:
            return True

        for item in self.sheet_items:
            if item.image_b64_txt is not None:
                return True

        return False

    def _is_need_sheet_prompt(self) -> bool:
        for item in self.sheet_items:
            if item.html is not None:
                return True
        return False

    def generate_openai(self,is_high_detail: bool = True, doc_title: str = "文档", just_return_image_prompt: bool = False) -> HumanMessage:
        if just_return_image_prompt:
            content_list: List[Dict[str, str]] = []
        else:
            content_list: List[Dict[str, str]] = [
                {
                    "type": "text",
                    "text": f"====== {doc_title} ======\n{self.md}"
                }
            ]

        if self._is_need_image_prompt():
            content_list.append(
                {
                    "type": "text",
                    "text": f"====== 以下是上述 <{doc_title}> 的图片ID及对应的图片，分析文档时必须结合图片 ======"
                }
            )
            image_detail = "high" if is_high_detail else "low"

            for item in self.sheet_items:
                if item.image_b64_txt is None:
                    continue

                content_list.append({
                    "type": "text",
                    "text": f"以下图片的ID: {item.raw_txt}"
                })
                content_list.append({
                    "type": "image_url",
                    "image_url": {
                        "detail": image_detail,
                        "url": f"data:image/png;base64,{item.b64_txt}"
                    }
                })

            for item in self.image_items:
                if item.b64_txt is None:
                    continue
                content_list.append({
                    "type": "text",
                    "text": f"以下图片的ID: {item.raw_txt}"
                })
                content_list.append({
                    "type": "image_url",
                    "image_url": {
                        "detail": image_detail,
                        "url": f"data:image/png;base64,{item.b64_txt}"
                    }
                })



        return HumanMessage(content=content_list)

    def generate_claude(self, doc_title = "文档", just_return_image_prompt: bool = False) -> HumanMessage:
        # https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters-anthropic-claude-messages.html
        if just_return_image_prompt:
            content_list: List[Dict[str, str]] = []
        else:
            content_list: List[Dict[str, str]] = [
                {
                    "type": "text",
                    "content": f"====== {doc_title} ======\n{self.md}"
                }

            ]
        if self._is_need_image_prompt():
            content_list.append(
                {
                    "type": "text",
                    "content": f"====== 以下是上述 <{doc_title}> 的图片ID及对应的图片，分析文档时必须结合图片 ======"
                }
            )
            for item in self.image_items:
                if item.b64_txt is None:
                    continue
                content_list.append({
                    "type": "text",
                    "content": f"以下图片的ID: {item.token}"
                })
                content_list.append(
                    {
                        "type": "image",
                        "source": {
                            "type": "base64",
                            "media_type": "image/png",
                            "data": item.b64_txt
                        }
                    },
                )
        return HumanMessage(content=content_list)

