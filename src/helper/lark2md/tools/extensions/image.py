import base64
import re
from typing import List, Dict, Optional

from pydantic.v1 import BaseModel, Field
from langchain_core.messages import  HumanMessage

import lark_oapi as lark
from ..lark import ImageApi

LLMType = str
OpenAIMode: LLMType = "openai"
ClaudeMode: LLMType = "claude"

class ImageItem(BaseModel):
    class Config:
        arbitrary_types_allowed = True

    alt_txt: str
    token: str
    raw_txt: str
    # b64_txt 的内容较大最好手动释放，至为 None
    b64_txt: Optional[str] = Field(default=None)
    # 10 分钟后自动过期
    tmp_lark_download_url: Optional[str] = Field(default=None)

def clear_image_items(image_items: List[ImageItem]):
    for item in image_items:
        item.b64_txt = None

class ImageParser:
    def __init__(self, md: str, lark_client: lark.Client):
        self.md = md
        self.image_api = ImageApi(lark_client)
        self._image_items = self._extract_all_image_ids()


    def _extract_all_image_ids(self) -> List[ImageItem]:
        image_pattern = r'!\[.*?\]\((.*?)\)'
        image_urls = re.findall(image_pattern, self.md)
        return [ImageItem(token=url, raw_txt=f"![image]({url})", alt_txt="image") for url in image_urls]

    def _split_list(self, lst, max_size):
        for i in range(0, len(lst), max_size):
            yield lst[i:i + max_size]

    def parse(self) -> List[ImageItem]:
        tmp_image_items: List[ImageItem] = []
        if len(self._image_items) == 0:
            return []
        # 由于 飞书接口最多限制 5 个图片一组
        split_image_items = list(self._split_list(self._image_items, 5))
        for image_items in split_image_items:
            image_tokens = [item.token for item in image_items]
            resp = self.image_api.get_image_url(image_tokens)
            if resp.data.tmp_download_urls is None:
                continue
            for d_url_obj in  resp.data.tmp_download_urls:
                for item in image_items:
                    if item.token == d_url_obj.file_token:
                        item.tmp_lark_download_url = d_url_obj.tmp_download_url
                        tmp_image_items.append(item)
        # 统一下载图片转成base64
        for item in tmp_image_items:
            if item.tmp_lark_download_url is None:
                continue
            try:
                image_data = self.image_api.download_image(item.tmp_lark_download_url)
                item.b64_txt = base64.b64encode(image_data.getvalue()).decode('utf-8')
            except Exception as e:
                lark.logger.exception(e)

        return tmp_image_items


if __name__ == "__main__":
    pass
