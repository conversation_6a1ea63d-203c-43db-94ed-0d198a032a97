import io
import re
from typing import Optional, Any, IO, List

import htmlmin
import lark_oapi as lark
import xlsx2html
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field


from ..lark import SheetApi


class SheetItem(BaseModel):
    class Config:
        arbitrary_types_allowed = True

    token: str
    sheet_id: str
    raw_txt: str
    io_buffer: Optional[IO[Any]] = Field(default=None)
    html: Optional[str] = Field(default=None)
    image_b64_txt: Optional[str] = Field(default=None)
    @classmethod
    def new(cls, spreadsheetToken_sheetId: str) -> "SheetItem":
        s = spreadsheetToken_sheetId.split("_")
        return cls(token=s[0], sheet_id=s[1], raw_txt=f"<sheet>{spreadsheetToken_sheetId}</sheet>")


def clear_sheet_items(sheet_items: List[SheetItem]):
    for idx, item in enumerate(sheet_items):
        item.io_buffer = None
        item.html = None
        item.image_b64_txt = None

class SheetParser:
    def __init__(self, md: str, lark_client: lark.Client):
        self.md = md
        self.lark_client = lark_client
        self.sheet_api = SheetApi(lark_client)
        self._sheet_items = self._extract_all_sheet_ids()
    def _extract_all_sheet_ids(self) -> List[SheetItem]:
        sheet_pattern = r'<sheet>(.*?)</sheet>'
        sheet_matches = re.findall(sheet_pattern, self.md, re.DOTALL)
        items = []
        if not sheet_matches:
            return items

        for i in sheet_matches:
            item = SheetItem.new(i)
            item.io_buffer = self.sheet_api.download_sheet(item.token, item.sheet_id)
            items.append(item)

        return items

    def parse_to_html(self) -> List[SheetItem]:
        res: List[SheetItem] = []
        for sheet_item in self._sheet_items:
            html_output = io.StringIO()
            xlsx2html.xlsx2html(sheet_item.io_buffer, html_output, sheet=sheet_item.sheet_id)
            table_pattern = re.compile(r'<table[^>]*>(.*?)</table>', re.DOTALL | re.IGNORECASE)
            # 搜索匹配项
            match = table_pattern.search(html_output.getvalue())
            if not match:
                continue

            sheet_item.html = htmlmin.minify(match.group(0))
            # with open("./lark2md/test/output.txt", "w") as fd:
            #     fd.write(sheet_item.html)
            res.append(sheet_item)
        return res

    def parse_to_image(self) -> IO[Any]:
        pass

    @classmethod
    def replace_html_into_markdown(cls, md: str, sheet_items: List[SheetItem]) -> str:
        for sheet_item in sheet_items:
            if sheet_item.html is None:
                continue
            md = md.replace(sheet_item.raw_txt, sheet_item.html)
        return md

class SheetPromptMessage:

    @classmethod
    def generate_openai_with_html(cls, md: str, sheet_items: List[SheetItem]) -> HumanMessage:
        pass

    @classmethod
    def generate_claude_with_html(cls):
        pass