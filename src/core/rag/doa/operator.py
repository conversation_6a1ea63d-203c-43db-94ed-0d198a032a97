from typing import Sequence

from src.log import logger
from sqlmodel import Session, select

from . import models
from .engine import engine


def get_all_person_knowledge_base(user_id: str) -> Sequence[models.KnowledgeBase]:
    with Session(engine) as session:
        statement = select(models.KnowledgeBase).where(
            models.KnowledgeBase.user_id == user_id
        )
        return session.exec(statement).all()


def create_person_knowledge_base(
        knowledge_base: models.KnowledgeBase,
) -> None:
    with Session(engine) as session:
        session.add(knowledge_base)
        session.commit()


def delete_person_knowledge_base(
        user_id: str,
        name: str,
) -> str:
    with Session(engine) as session:
        statement = (
            select(models.KnowledgeBase)
            .where(models.KnowledgeBase.file_name == name)
            .where(models.KnowledgeBase.user_id == user_id)
        )
        results = session.exec(statement)
        k = results.one_or_none()
        if k is None:
            return ""
        session.delete(k)
        session.commit()
        return k.query_index


def get_all_common_knowledge_base() -> Sequence[models.CommonKnowledgeBase]:
    with Session(engine) as session:
        statement = select(models.CommonKnowledgeBase)
        return session.exec(statement).all()


def get_parent_all_file_info(parent_id: int) -> Sequence[models.FileInfo]:
    with Session(engine) as session:
        statement = select(models.FileInfo).where(
            models.FileInfo.parent_id == parent_id
        )
        results = session.exec(statement)
        return results.all()


# create FileInfo
def create_file_info(parend_id: int, token: str, update_time: int, source: str) -> None:
    file_info = models.FileInfo(
        parent_id=parend_id, token=token, update_time=update_time, source=source
    )
    with Session(engine) as session:
        session.add(file_info)
        session.commit()


# get FileInfo by parent_id and token
def get_file_info(parent_id: int, token: str) -> models.FileInfo | None:
    with Session(engine) as session:
        statement = (
            select(models.FileInfo)
            .where(models.FileInfo.parent_id == parent_id)
            .where(models.FileInfo.token == token)
        )
        results = session.exec(statement)
        return results.one_or_none()


def update_or_create_file_info(
        parent_id: int, token: str, update_time: int, source: str
) -> None:
    with Session(engine) as session:
        statement = (
            select(models.FileInfo)
            .where(models.FileInfo.parent_id == parent_id)
            .where(models.FileInfo.token == token)
        )
        results = session.exec(statement)
        file_info = results.one_or_none()
        if file_info is None:
            logger.info(f"file_info :{source} is None")
            file_info = models.FileInfo(
                parent_id=parent_id, token=token, update_time=update_time, source=source
            )
        else:
            file_info.update_time = update_time
        session.add(file_info)
        session.commit()


# update FileInfo update_time by parent_id and token
def update_file_info(parent_id: int, token: str, update_time: int) -> None:
    with Session(engine) as session:
        statement = (
            select(models.FileInfo)
            .where(models.FileInfo.parent_id == parent_id)
            .where(models.FileInfo.token == token)
        )
        results = session.exec(statement)
        file_info = results.one_or_none()
        if file_info is None:
            return
        file_info.update_time = update_time
        session.add(file_info)
        session.commit()


# delete FileInfo by parent_id and source
def delete_file_info(parent_id: int, source: str) -> None:
    with Session(engine) as session:
        statement = (
            select(models.FileInfo)
            .where(models.FileInfo.parent_id == parent_id)
            .where(models.FileInfo.source == source)
        )
        results = session.exec(statement)
        file_info = results.one_or_none()
        if file_info is None:
            return
        session.delete(file_info)
        session.commit()


# delete FileInfo by parent_id
def delete_file_info_by_parent_id(parent_id: int) -> None:
    with Session(engine) as session:
        statement = select(models.FileInfo).where(
            models.FileInfo.parent_id == parent_id
        )
        results = session.exec(statement)
        file_info = results.all()
        for f in file_info:
            session.delete(f)
        session.commit()
