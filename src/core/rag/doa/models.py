import datetime
from typing import Optional

from sqlmodel import Field, SQLModel


class KnowledgeBase(SQLModel, table=True):
    __tablename__: str = "knowledgebase"
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: str = Field(index=True)
    query_index: str
    file_name: str = Field(index=True)
    create_time: datetime.datetime = Field(default_factory=datetime.datetime.now)


class CommonKnowledgeBase(SQLModel, table=True):
    __tablename__: str = "common_knowledgebase"
    id: Optional[int] = Field(default=None, primary_key=True)
    source_type: str = Field(nullable=False)
    space_id: Optional[str] = Field(default=None)
    description: str
    dir_token: str
    space_prefix: Optional[str] = Field(default=None)
    create_time: datetime.datetime = Field(default_factory=datetime.datetime.now)
    query_index: str


class FileInfo(SQLModel, table=True):
    __tablename__: str = "knowledgebase_file_info"
    id: Optional[int] = Field(default=None, primary_key=True)
    parent_id: int = Field(index=True, nullable=False)
    token: str = Field(nullable=False)
    update_time: int = Field(default=0)
    source: str = Field(nullable=False)
