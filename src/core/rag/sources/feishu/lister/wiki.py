import re
import time
from queue import SimpleQueue

import lark_oapi as lark
import tenacity
from lark_oapi.api.wiki.v2 import (
    GetNodeSpaceRequest,
    GetNodeSpaceResponse,
    GetNodeSpaceResponseBody,
    ListSpaceNodeRequest,
    ListSpaceNodeResponse,
    ListSpaceNodeResponseBody,
)
from lark_oapi.api.wiki.v2.model import Node
from src.log import logger

from src.core.rag.sources.feishu.lister.file import FileInfo


class SpaceWalker:
    def __init__(
        self,
        client: lark.Client,
        space_id: str,
        wiki_prefix: str,
        start_folder_token: str,
        ignore: str = "",
    ) -> None:
        self.client = client
        self.space_id = space_id
        self.start_folder_token = start_folder_token
        self.wiki_prefix = wiki_prefix
        self._items: "SimpleQueue[Node]" = SimpleQueue()
        self._init_queue()
        self.ignore = re.compile(r"{ignore}".format(ignore=ignore))

    def _init_queue(self):
        rsp = self._get_spaces_node_info(self.start_folder_token)
        assert rsp is not None
        node = rsp.node
        if node is None:
            return
        self._items.put(node)

    def _list_sub_docs(
        self, node_token: str, page_token: str = ""
    ) -> ListSpaceNodeResponseBody | None:
        nodeReq: ListSpaceNodeRequest = (
            ListSpaceNodeRequest.builder()
            .space_id(self.space_id)
            .page_token(page_token)
            .page_size(50)
            .parent_node_token(node_token)
            .build()
        )
        assert self.client.wiki is not None
        nodeRsp: ListSpaceNodeResponse = self.client.wiki.v2.space_node.list(nodeReq)
        if not nodeRsp.success():
            if nodeRsp.code == 99991400:
                time.sleep(20)
            e = f"Failed to get node. code: {nodeRsp.code}, msg: {nodeRsp.msg}"
            logger.error(e)
            raise Exception(e)
        return nodeRsp.data

    def _get_spaces_node_info(self, node_token) -> GetNodeSpaceResponseBody | None:
        request: GetNodeSpaceRequest = (
            GetNodeSpaceRequest.builder().token(node_token).build()
        )

        assert self.client.wiki is not None
        response: GetNodeSpaceResponse = self.client.wiki.v2.space.get_node(request)
        if not response.success():
            if response.code == 99991400:
                time.sleep(20)
            e = f"Failed to get node. code: {response.code}, msg: {response.msg}, node_token: {node_token}"
            logger.error(e)
            raise Exception(e)
        return response.data

    @tenacity.retry(stop=tenacity.stop_after_attempt(3), wait=tenacity.wait_fixed(10))
    def _put_children_into_queue(self, d: Node):
        page_token = ""
        while True:
            assert d.node_token is not None
            datas = self._list_sub_docs(d.node_token, page_token=page_token)
            assert datas is not None
            if datas.items is not None:
                for item in datas.items:
                    self._items.put(item)
                if not datas.has_more:
                    break
                page_token = datas.page_token
            break

    def __iter__(self):
        return self

    def __next__(self) -> FileInfo:
        if self._items.empty():
            raise StopIteration
        while True:
            if self._items.empty():
                raise StopIteration
            d = self._items.get()
            if d.has_child:
                self._put_children_into_queue(d)
            assert d.title is not None
            if self.ignore.search(d.title):
                logger.info(f"skip ignore title {d.title} {d.node_token}")
                continue
            if d.obj_type not in ["doc", "docx", "pdf"]:
                logger.info(f"skip not doc type {d.title} {d.node_token}")
                continue
            return FileInfo(d.title, d.obj_token, d.obj_type, f"{self.wiki_prefix}{d.node_token}", int(d.obj_edit_time))  # type: ignore
