from queue import SimpleQueue

import lark_oapi as lark
import tenacity
from lark_oapi.api.drive.v1 import (
    ListFileRequest,
    ListFileRequestBuilder,
    ListFileResponse,
    ListFileResponseBody,
)
from lark_oapi.api.drive.v1.model.file import File
from src.log import logger

from src.core.rag.sources.feishu.lister.file import FileInfo


class FolderWalker:

    def __init__(
            self,
            client: lark.Client,
            folder_token: str,
    ) -> None:
        self.client = client
        self.folder_token = folder_token
        self._items: "SimpleQueue[File]" = SimpleQueue()
        self.page_token = ""
        self._fill_queue()

    def _fill_queue(self):
        body = self._list_sub_docs(self.folder_token, self.page_token)
        if body is None or body.files is None:
            return
        for f in body.files:
            self._items.put(f)
        if body.has_more:
            self.page_token = body.next_page_token
        else:
            self.page_token = ""

    @tenacity.retry(
        stop=tenacity.stop_after_attempt(3),
        wait=tenacity.wait_exponential(multiplier=2, max=10),
    )
    def _list_sub_docs(
            self, folder_token: str, page_token: str | None = None
    ) -> ListFileResponseBody | None:

        request_builder: ListFileRequestBuilder = (
            ListFileRequest.builder()
            .folder_token(folder_token)
            .order_by("EditedTime")
            .direction("DESC")
            .page_size(200)
        )
        if page_token is not None:
            request_builder.page_token(page_token)

        # 发起请求
        assert self.client.drive is not None
        response: ListFileResponse = self.client.drive.v1.file.list(
            request_builder.build()
        )

        # 处理失败返回
        if not response.success():
            logger.error(
                f"client.drive.v1.file.list failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
            )
            raise Exception(
                f"client.drive.v1.file.list failed, code: {response.code}, msg: {response.msg}"
            )
        return response.data

    def __iter__(self):
        return self

    def __next__(self) -> FileInfo:
        if self._items.empty() and self.page_token == "":
            raise StopIteration
        while True:
            if self._items.empty() and self.page_token == "":
                raise StopIteration
            if self._items.empty() and self.page_token != "":
                self._fill_queue()
            d = self._items.get()
            if d.type not in ["doc", "docx"]:
                logger.info(f"skip not doc type {d.name} {d.token}")
                continue
            return FileInfo(d.name, d.token, d.type, d.url, int(d.modified_time))  # type: ignore
