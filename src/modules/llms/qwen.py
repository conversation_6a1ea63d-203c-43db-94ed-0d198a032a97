from typing import Dict

from langchain.schema.language_model import BaseLanguageModel
from langchain_community.chat_models.tongyi import ChatTongyi

from .builder import B<PERSON><PERSON>, LLMArgs


class QWenBuilder(Builer):
    def __init__(self):
        self.args = LLMArgs()

    def buildConfig(self, config: Dict) -> Builer:
        self.args.temperature = config["temperature"]
        self.args.model = config["model"]
        return self

    def build(self) -> BaseLanguageModel:
        return ChatTongyi(
            top_p=self.args.temperature,
            model=self.args.model,
            client=None,
            api_key=None,
        )
