from typing import Dict

from langchain.schema.language_model import BaseLanguageModel
from langchain_core.embeddings import Embeddings
from langchain_openai import AzureOpenAIEmbeddings, OpenAIEmbeddings
from langchain_openai.chat_models import AzureChatOpenAI

from .builder import <PERSON><PERSON><PERSON>, EmbeddingsBuiler, LLMArgs


class AzureChatOpenAIBuilder(Builer):
    def __init__(self) -> None:
        self.args = LLMArgs()

    def buildConfig(self, config: Dict) -> Builer:
        self.args.endpoint = config["endpoint"]
        self.args.temperature = config["temperature"]
        self.args.api_version = config["api_version"]
        self.args.model = config["model"]
        self.args.deployment = config["deployment"]
        self.args.timeout = config.get("timeout", 60)
        return self

    def build(self) -> BaseLanguageModel:
        return AzureChatOpenAI(
            temperature=0,
            azure_endpoint=self.args.endpoint,
            azure_deployment=self.args.deployment,
            api_version=self.args.api_version,
            model=self.args.model,
            timeout=self.args.timeout,
        )


class OpenAIEmbeddingsBuilder(EmbeddingsBuiler):
    def __init__(self):
        self.args = {}

    def buildConfig(self, config: Dict) -> EmbeddingsBuiler:
        self.args = config["openai"]
        return self

    def build(self) -> Embeddings:
        return AzureOpenAIEmbeddings(**self.args)
        return OpenAIEmbeddings(**self.args)
