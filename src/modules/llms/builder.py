from abc import ABC, abstractmethod
from typing import Dict

from langchain.schema.language_model import BaseLanguageModel
from langchain_core.embeddings import Embeddings


class LLMArgs:
    def __init__(self):
        self.temperature = 0
        self.endpoint = ""
        self.deployment = ""
        self.model = ""
        self.timeout = 10
        self.api_version = ""


class Builer(ABC):
    @abstractmethod
    def buildConfig(self, config: Dict) -> "Builer":
        raise

    @abstractmethod
    def build(self) -> BaseLanguageModel:
        raise


class EmbeddingsBuiler(ABC):
    @abstractmethod
    def buildConfig(self, config: Dict) -> "Builer":
        raise

    @abstractmethod
    def build(self) -> Embeddings:
        raise
