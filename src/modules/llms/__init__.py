import hashlib
import os
from enum import Enum
from typing import Dict

from gptcache import Cache
from gptcache.manager.factory import manager_factory
from gptcache.processor.pre import get_prompt
from langchain_community.cache import GPTCache
from langchain_core.globals import set_llm_cache, set_verbose

from .qwen import QWenBuilder


def get_hashed_name(name):
    return hashlib.sha256(name.encode()).hexdigest()


def init_gptcache(cache_obj: Cache, llm: str):
    hashed_llm = get_hashed_name(llm)
    cache_obj.init(
        pre_embedding_func=get_prompt,
        data_manager=manager_factory(
            manager="map", data_dir=f"/tmp/map_cache_{hashed_llm}"
        ),
    )


if os.environ.get("ENABLE_CACHE", "False") == "True":
    set_llm_cache(GPTCache(init_gptcache))

if os.environ.get("VERBOSE", "False") == "True":
    set_verbose(True)

from langchain_core.embeddings import Embeddings

from .azure import AzureChatOpenAIBuilder, OpenAIEmbeddingsBuilder
from .huggingface import HuggingFaceEmbeddingsBuilder


class LLMType(Enum):
    OPENAI = 1
    OLLAMA = 2
    BEDROCK = 3
    QWEN = 4
    MOONSHOT = 5


def ConfigTypeToLLMType(t: str) -> LLMType:
    if t == "openai":
        return LLMType.OPENAI
    elif t == "ollama":
        return LLMType.OLLAMA
    elif t == "bedrock":
        return LLMType.BEDROCK
    elif t == "qwen":
        return LLMType.QWEN
    elif t == "moonshot":
        return LLMType.MOONSHOT
    else:
        raise Exception(f"Invalid LLM name: {t}")


class EmbeddingType(Enum):
    OPENAI = 1
    HUGGINGFACE = 2


class LLMFactory:
    def __init__(self, cfg: Dict):
        self.cfg = cfg

    def BuildLLMByName(self, name: str):
        if name not in self.cfg:
            raise Exception(f"Invalid LLM name: {name}")

        cfg = self.cfg[name]

        tp = ConfigTypeToLLMType(cfg["type"])
        if tp == LLMType.OPENAI:
            return AzureChatOpenAIBuilder().buildConfig(cfg).build()
        elif tp == LLMType.QWEN:
            return QWenBuilder().buildConfig(cfg).build()
        else:
            raise Exception(f"Invalid LLM type: {tp}")

    def BuildEmbedding(self, tp: EmbeddingType) -> Embeddings:
        if tp == EmbeddingType.OPENAI:
            return OpenAIEmbeddingsBuilder().buildConfig(self.cfg).build()
        elif tp == EmbeddingType.HUGGINGFACE:
            return HuggingFaceEmbeddingsBuilder().buildConfig(self.cfg).build()
        else:
            raise Exception(f"Invalid Embedding type: {tp}")
