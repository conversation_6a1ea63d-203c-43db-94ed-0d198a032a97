from typing import Dict

from langchain_core.embeddings import Embeddings
from langchain_huggingface import HuggingFaceEndpointEmbeddings

from .builder import EmbeddingsBuiler


class HuggingFaceEmbeddingsBuilder(EmbeddingsBuiler):
    def __init__(self):
        self.args = {}

    def buildConfig(self, config: Dict) -> EmbeddingsBuiler:
        self.args = {
            "model": config.model
        }
        return self

    def build(self) -> Embeddings:
        return HuggingFaceEndpointEmbeddings(
            model=self.args["model"], client=None, async_client=None
        )
