from typing import Optional, List, Type

from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings
from langchain_core.language_models import LanguageModelLike
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from src.log import logger
from src.mcp.chain import create_milvus_qa_chain
from src.mcp.retriever import HybridMilvusRetriever


class VectoryDBBaseTool(BaseModel):
    em: Embeddings
    doc_llm: LanguageModelLike

    class Config:
        arbitrary_types_allowed = True


class QueryVectorDBArgs(BaseModel):
    query: str = Field(..., description="查询的内容描述")


class QueryVectorDB(VectoryDBBaseTool, BaseTool):
    name: str
    args_schema: Type[BaseModel] = QueryVectorDBArgs
    return_direct: bool = True
    description: str
    query_index: str

    def _run(
            self, query: str, run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> List[Document] | str:
        retriever = HybridMilvusRetriever(collection_name=self.query_index)

        chain = create_milvus_qa_chain(
            llm=self.doc_llm,  # type: ignore
            retriever=retriever,
        )

        rtn = chain.invoke(
            {"question": query},
            config={"callbacks": run_manager.get_child() if run_manager else None},
        )
        logger.info(f"结果：{rtn}")
        if "answer" not in rtn or rtn["answer"] is None or rtn["answer"] == "":
            return "The relevant information is not included in the knowledge base."
        return rtn["answer"] + ("\n**参考文档**:\n" + rtn["source"] if 'source' in rtn else '')
