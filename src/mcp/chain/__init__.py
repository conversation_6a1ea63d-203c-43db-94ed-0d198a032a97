import logging
from typing import List, Dict, Any

from langchain.output_parsers.fix import OutputFixingParser
from langchain.output_parsers.json import SimpleJsonOutputParser
from langchain.prompts import PromptTemplate
from langchain_core.documents import Document
from langchain_core.language_models import BaseLanguageModel
from langchain_core.retrievers import BaseRetriever

logger = logging.getLogger(__name__)

template = """
功能要求：
根据给出的长文档的Markdown片段，选取其中有用的片段进行参考，回答用户的问题。

回答要求：
1.永远使用中文回答问题。
2.您的答案应该是精简、清晰的，有依据，不能与用户问题无关
3.如所有片段对于用户问题都无参考意义，请返回空字符串 ""，不要试图编造答案误导用户
4.您的答案只根据给定片段内容作答，不允许编造、推测、虚构。
5.每条回答都要附上来源，格式为 [文档标题](URL)，如有多个片段来源，请用换行分隔。
6.您的答案中应带上片段的来源，仅带上有用的片段。

输出格式：
请严格按照以下 JSON 格式返回您的答案：
{{
    "answer": "你的答案在这里，内容必须条理清晰，回答准确",
    "source": "你引用的片段来源，格式为:[文档标题](URL)，多个用换行隔开"
}}

用户问题:
{question}

片段格式：
<content>片段内容</content>
<source>[文档标题](文档URL)</source>
>>>>>>>（片段之间用这个分隔符分隔）

片段内容:
{context}
"""

DEFAULT_PROMPT = PromptTemplate(
    template=template, input_variables=["context", "question"]
)


def _combine_documents(docs: List[Document], document_separator="\n\n") -> str:
    doc_strings = [
        (f"<source>[{doc.metadata['title']}]({doc.metadata['source']})"
         f"</source>\n<content>{doc.page_content}</content>\n>>>>>>>")
        for doc in docs
    ]
    return document_separator.join(doc_strings)


class MilvusQAChain:
    """传统方式的 Milvus QA 链，不使用管道操作符"""

    def __init__(
            self,
            llm: BaseLanguageModel,
            retriever: BaseRetriever,
            prompt: PromptTemplate = DEFAULT_PROMPT,
    ):
        self.llm = llm
        self.retriever = retriever
        self.prompt = prompt
        self.parser = OutputFixingParser(retry_chain=llm, parser=SimpleJsonOutputParser())

    def invoke(self, input_data: Dict[str, Any], config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行 QA 链的主要逻辑

        Args:
            input_data: 包含 "question" 字段的输入数据
            config: 配置信息，包含回调等

        Returns:
            包含答案和来源的字典
        """
        question = input_data["question"]

        # 1. 使用检索器获取相关文档
        docs = self.retriever.invoke(
            question,
            callbacks=config.get("callbacks") if config else None
        )

        if not docs:
            return {
                "answer": "",
                "source": ""
            }

        context = _combine_documents(docs)
        formatted_input = {
            "context": context,
            "question": question
        }

        # 3. 使用提示模板格式化
        formatted_prompt = self.prompt.format(**formatted_input)
        # 4. 调用 LLM
        llm_response = self.llm.invoke(formatted_prompt)

        # 5. 解析输出
        parsed_result = self.parser.parse(llm_response.content)

        return parsed_result


def create_milvus_qa_chain(
        llm: BaseLanguageModel,
        retriever: BaseRetriever,
        prompt: PromptTemplate = DEFAULT_PROMPT,
) -> MilvusQAChain:
    """
    创建 Milvus QA 链

    Args:
        llm: 语言模型
        retriever: 文档检索器
        prompt: 提示模板

    Returns:
        MilvusQAChain 实例
    """
    return MilvusQAChain(llm, retriever, prompt)
