# LangGraph MixQueryTool

基于 LangGraph 的融合查询工具，使用节点流转执行模式重构了原有的 MixQueryTool。

## 概述

本项目提供了两个版本的 LangGraph 融合查询工具：

1. **基础版本** (`LangGraphMixQueryTool`): 简单的节点流转实现
2. **高级版本** (`AdvancedLangGraphMixQueryTool`): 包含条件路由、错误处理和检查点功能

## 主要特性

### 基础版本特性
- 🔄 节点流转执行模式
- 📝 意图解析节点
- 🔍 并发查询节点
- 📊 结果聚合节点
- 🚀 简单易用

### 高级版本特性
- 🎯 条件路由决策
- ❌ 完善的错误处理
- 💾 检查点支持（可恢复执行）
- 📈 状态跟踪
- 🔀 智能查询路径选择

## 架构设计

### 节点流转图

```mermaid
graph TD
    A[开始] --> B[意图解析节点]
    B --> C{是否有错误?}
    C -->|是| D[错误处理节点]
    C -->|否| E[资源检查节点]
    E --> F{是否包含资源?}
    F -->|否| G[仅文档查询节点]
    F -->|是| H[文档和资源并发查询节点]
    G --> I[结果聚合节点]
    H --> I
    I --> J[结束]
    D --> J
```

### 状态管理

每个节点都会更新和传递状态，包括：
- `query`: 原始查询
- `parsed_intents`: 解析的意图列表
- `doc_results`: 文档查询结果
- `resource_results`: 资源查询结果
- `final_answer`: 最终答案
- `error`: 错误信息
- `processing_stage`: 处理阶段

## 安装和依赖

确保已安装以下依赖：

```bash
poetry add langgraph
```

或者使用 pip：

```bash
pip install langgraph
```

## 使用方法

### 基础版本使用

```python
from src.mcp.mix_query import LangGraphMixQueryTool

# 创建工具实例
tool = LangGraphMixQueryTool(collection_name="knowledge_base_of_sre_v4")

# 执行查询
result = tool._run("Redis集群如何配置高可用？")
print(result)
```

### 高级版本使用

```python
from src.mcp.mix_query import AdvancedLangGraphMixQueryTool

# 创建工具实例
tool = AdvancedLangGraphMixQueryTool(collection_name="knowledge_base_of_sre_v4")

# 不启用检查点的查询
result = tool._run("Redis集群如何配置高可用？", enable_checkpoint=False)

# 启用检查点的查询（支持恢复）
result = tool._run("复杂查询问题", enable_checkpoint=True)
print(result)
```

### 作为 LangChain Tool 使用

```python
from langchain.agents import initialize_agent
from src.mcp.mix_query import AdvancedLangGraphMixQueryTool

# 创建工具
mix_query_tool = AdvancedLangGraphMixQueryTool(
    collection_name="knowledge_base_of_sre_v4"
)

# 在 Agent 中使用
tools = [mix_query_tool]
agent = initialize_agent(tools, llm, agent="zero-shot-react-description")

result = agent.run("我想了解Redis集群的配置方法")
```

## 配置选项

### 基础配置

```python
tool = LangGraphMixQueryTool(
    collection_name="your_collection_name",  # 必需：知识库集合名称
)
```

### 高级配置

```python
tool = AdvancedLangGraphMixQueryTool(
    collection_name="your_collection_name",  # 必需：知识库集合名称
    max_retries=3,                          # 可选：最大重试次数
)
```

## 性能对比

| 特性 | 原始工具 | 基础LangGraph | 高级LangGraph |
|------|----------|---------------|---------------|
| 执行模式 | 线性执行 | 节点流转 | 条件路由 |
| 错误处理 | 基础 | 基础 | 完善 |
| 状态管理 | 无 | 简单 | 完整 |
| 可恢复性 | 无 | 无 | 支持 |
| 可视化 | 无 | 支持 | 支持 |
| 调试能力 | 有限 | 良好 | 优秀 |

## 示例和测试

### 运行示例

```bash
cd src/mcp/mix_query
python example_usage.py
```

### 运行测试

```bash
cd src/mcp/mix_query
python -m pytest test_langgraph_tools.py -v
```

或者使用 unittest：

```bash
python test_langgraph_tools.py
```

## 错误处理

### 常见错误和解决方案

1. **意图解析失败**
   - 检查 IntentParser 配置
   - 确认 LLM 服务可用

2. **知识库查询失败**
   - 验证 collection_name 是否正确
   - 检查 Milvus 连接

3. **CMDB查询失败**
   - 确认 MCP 服务可用
   - 检查网络连接

### 错误日志

工具会记录详细的错误日志，可以通过以下方式查看：

```python
import logging
logging.basicConfig(level=logging.INFO)

# 执行查询，错误信息会输出到日志
result = tool._run("查询内容")
```

## 扩展和自定义

### 添加新节点

```python
def custom_node(state: AdvancedMixQueryState) -> AdvancedMixQueryState:
    """自定义节点"""
    # 处理逻辑
    return {
        **state,
        "custom_field": "custom_value"
    }

# 在图中添加节点
workflow.add_node("custom_node", custom_node)
```

### 自定义路由逻辑

```python
def custom_router(state: AdvancedMixQueryState) -> str:
    """自定义路由逻辑"""
    if state.get("custom_condition"):
        return "custom_path"
    return "default_path"

workflow.add_conditional_edges(
    "source_node",
    custom_router,
    {
        "custom_path": "custom_node",
        "default_path": "default_node"
    }
)
```

## 最佳实践

1. **选择合适的版本**
   - 简单场景使用基础版本
   - 复杂场景使用高级版本

2. **合理使用检查点**
   - 长时间运行的查询启用检查点
   - 简单查询可以关闭检查点以提高性能

3. **错误处理**
   - 总是检查返回结果中的错误信息
   - 实现适当的重试逻辑

4. **性能优化**
   - 合理设置并发数量
   - 监控查询性能

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个工具。

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
poetry install

# 运行测试
poetry run pytest
```

## 许可证

本项目遵循项目根目录的许可证。
