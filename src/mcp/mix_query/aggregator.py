from typing import List, Dict

from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import SimpleJsonOutputParser
from langchain_core.prompts import PromptTemplate

from src.helper.client.model import get_chat_model
from src.log import logger


class ResultAggregator:
    """结果聚合器，使用大模型对查询结果进行聚合处理"""

    def __init__(self):
        self.llm = get_chat_model("doc_llm")
        self.prompt_template = self._create_prompt_template()
        self.parser = OutputFixingParser(retry_chain=self.llm, parser=SimpleJsonOutputParser())

    def _create_prompt_template(self) -> PromptTemplate:
        """创建结果聚合的提示模板"""
        template = """
===== 角色 =====
你是一个专业的运维知识助手，能够选择与用户意图相关文档和资源信息，为用户提供准确、全面的回答。

===== 用户问题 =====
{original_query}

===== 用户意图分析 =====
{parsed_intents}

===== 检索结果（文档） =====
{doc_results}

===== 检索结果（资源） =====
{resource_results}

===== 回答要求 =====
1. 根据用户问题和检索到的信息，提供准确、全面的回答
2. 优先使用检索到的具体信息，避免编造内容
3. 如果文档和资源信息有关联，请进行关联分析，文档为向量数据库获取不一定准确，请仔细甄别可用性
4. 回答要条理清晰，不要使用原文格式直接回答，要对内容进行整理，再输出markdown
5. 如所有文档对于用户问题都无参考意义，请返回空字符串 ""，不要试图编造答案误导用户
5. 如果信息不足以回答问题，请明确说明
6. 在回答末尾提供相关的参考来源

===== 输出格式 =====
请严格按照以下 JSON 格式返回您的答案：
{{
    "answer": "你的答案在这里，内容必须条理清晰，回答准确",
    "source": "你引用的文档来源，格式为:[文档标题](URL)，多个用换行隔开"
}}

请开始回答：
"""
        return PromptTemplate(
            template=template,
            input_variables=["original_query", "parsed_intents", "doc_results", "resource_results"]
        )

    def aggregate(self, original_query: str, parsed_intents: List[Dict],
                  doc_results: List[Dict], resource_results: List[Dict]) -> str:
        """
        聚合查询结果

        Args:
            original_query: 原始用户查询
            parsed_intents: 解析的用户意图
            doc_results: 文档查询结果
            resource_results: 资源查询结果

        Returns:
            聚合后的回答
        """
        try:
            # 格式化意图信息
            intents_text = self._format_intents(parsed_intents)

            # 格式化文档结果
            docs_text = self._format_doc_results(doc_results)

            # 格式化资源结果
            resources_text = self._format_resource_results(resource_results)

            # 构建提示
            formatted_prompt = self.prompt_template.format(
                original_query=original_query,
                parsed_intents=intents_text,
                doc_results=docs_text,
                resource_results=resources_text
            )

            # 调用LLM生成回答
            response = self.llm.invoke(formatted_prompt)

            rtn = self.parser.parse(response.content)

            logger.info(f"结果：{rtn}")
            if "answer" not in rtn or rtn["answer"] is None or rtn["answer"] == "":
                return "The relevant information is not included in the knowledge base."
            return rtn["answer"] + ("\n**参考文档**:\n" + rtn["source"] if 'source' in rtn else '')

        except Exception as e:
            logger.error(f"结果聚合失败: {e}")
            return "The relevant information is not included in the knowledge base."

    def _format_intents(self, parsed_intents: List[Dict]) -> str:
        """格式化意图信息"""
        if not parsed_intents:
            return "无法解析用户意图"

        intents_text = ""
        for i, intent in enumerate(parsed_intents, 1):
            intents_text += f"**意图{i}**：{intent.get('intention', '未知')}\n"
            if intent.get('keywords'):
                intents_text += f"- 关键词：{', '.join(intent['keywords'])}\n"
            if intent.get('resources'):
                intents_text += f"- 涉及资源：{', '.join(intent['resources'])}\n"
            intents_text += "\n"

        return intents_text

    def _format_doc_results(self, doc_results: List[Dict]) -> str:
        """格式化文档查询结果"""
        if not doc_results:
            return "未找到相关文档信息"

        docs_text = ""
        for i, doc_result in enumerate(doc_results, 1):
            try:
                results = doc_result.get('result', {}) if isinstance(doc_result, dict) else {}
                intent = doc_result.get('intent', {}) if isinstance(doc_result, dict) else {}

                if isinstance(intent, dict):
                    docs_text += f"* 查询意图：{intent.get('intention', '未知')}\n"
                else:
                    docs_text += f"* 查询意图：未知\n"

                for result in results:
                    docs_text += f"* 文档标题：<title>{result.metadata.get('title', '')}</title>\n"
                    docs_text += f"* 文档内容：\n<markdown>\n{result.page_content}\n</markdown>\n"
                    docs_text += f"* 来源：<source>{result.metadata.get('source', '')}</source>\n\n"
            except Exception as e:
                logger.error(f"格式化文档结果失败 {i}: {e}")
                continue

        return docs_text if docs_text.strip() else "未找到有效的文档信息"

    def _format_resource_results(self, resource_results: List[Dict]) -> str:
        """格式化资源查询结果"""
        if not resource_results:
            return "未找到相关资源信息"

        resources_text = ""
        for i, resource_result in enumerate(resource_results, 1):
            try:
                # 安全获取资源信息
                if not isinstance(resource_result, dict):
                    logger.warning(f"资源结果不是字典类型: {type(resource_result)}")
                    continue

                resource_name = resource_result.get('resource', '未知资源')
                result = resource_result.get('result', {})

                resources_text += f"* 资源：{resource_name}\n"
                if isinstance(result, dict) and result.get('data'):
                    data = result['data']
                    if isinstance(data, dict):
                        resources_text += f"<resource>\n{self._format_resource_data(data)}\n</resource>\n"
                    else:
                        resources_text += f"<resource>\n{str(data)}\n</resource>\n"
                elif result:
                    resources_text += f"<resource>\n{str(result)}\n</resource>\n"

                resources_text += "\n"

            except Exception as e:
                logger.error(f"格式化资源结果失败 {i}: {e}")
                continue

        return resources_text if resources_text.strip() else "未找到有效的资源信息"

    def _format_resource_data(self, data: Dict) -> str:
        """格式化资源数据"""
        if not isinstance(data, dict):
            return str(data)

        formatted = ""
        for key, value in data.items():
            if isinstance(value, (dict, list)):
                formatted += f"**{key}**: {str(value)}\n"
            else:
                formatted += f"**{key}**: {value}\n"

        return formatted
