"""
高级 LangGraph 融合查询工具
包含条件路由、错误处理和状态检查点
"""
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import TypedDict, List, Dict, Any, Optional, Type, Literal

from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.documents import Document
from langchain_core.tools import BaseTool
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.types import TextContent, ImageContent, EmbeddedResource
from pydantic import Field, BaseModel

from src.log import logger
from .aggregator import ResultAggregator
from .intent_parser import IntentParser
from ...helper.client.milvus import hybrid_search_optimized


class AdvancedMixQueryState(TypedDict):
    """高级 LangGraph 状态定义"""
    query: str
    parsed_intents: List[Dict[str, Any]]
    doc_results: List[Dict[str, Any]]
    resource_results: List[Dict[str, Any]]
    final_answer: str
    error: Optional[str]
    has_resources: bool
    retry_count: int
    processing_stage: str


class AdvancedLangGraphMixQueryArgs(BaseModel):
    query: str = Field(..., description="查询的内容描述")
    enable_checkpoint: bool = Field(default=False, description="是否启用检查点")


class AdvancedLangGraphMixQueryTool(BaseTool):
    """高级 LangGraph 融合查询工具"""

    name: str = "advanced_langgraph_mix_query"
    args_schema: Type[BaseModel] = AdvancedLangGraphMixQueryArgs
    return_direct: bool = True
    description: str = "高级LangGraph融合查询工具，支持条件路由、错误处理和检查点恢复"
    collection_name: str
    max_retries: int = 3

    def __init__(self, collection_name: str, **kwargs):
        super().__init__(collection_name=collection_name, **kwargs)
        # 使用 object.__setattr__ 来避免 Pydantic 的字段验证
        object.__setattr__(self, 'memory', MemorySaver())
        object.__setattr__(self, 'graph', self._build_advanced_graph())

    def _run(
            self,
            query: str,
            enable_checkpoint: bool = False,
            run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """
        执行高级融合查询

        Args:
            query: 用户查询问题
            enable_checkpoint: 是否启用检查点
            run_manager: 回调管理器

        Returns:
            聚合后的查询结果
        """
        try:
            logger.info(f"开始高级LangGraph融合查询: {query}")

            # 初始化状态
            initial_state = AdvancedMixQueryState(
                query=query,
                parsed_intents=[],
                doc_results=[],
                resource_results=[],
                final_answer="",
                error=None,
                has_resources=False,
                retry_count=0,
                processing_stage="init"
            )

            # 配置执行参数
            config = {"configurable": {"thread_id": f"query_{hash(query)}"}} if enable_checkpoint else None

            # 执行图
            result = self.graph.invoke(initial_state, config=config)

            if result.get("error"):
                logger.error(f"高级LangGraph查询失败: {result['error']}")
                return f"查询过程中出现错误: {result['error']}"

            logger.info(f"高级LangGraph融合查询完成")
            return result["final_answer"]

        except Exception as e:
            logger.error(f"高级LangGraph融合查询失败: {e}")
            return f"查询过程中出现错误: {str(e)}"

    def _build_advanced_graph(self) -> StateGraph:
        """构建高级 LangGraph 执行图"""

        # 创建状态图
        workflow = StateGraph(AdvancedMixQueryState)

        # 添加节点
        workflow.add_node("parse_intent", self._parse_intent_node)
        workflow.add_node("check_resources", self._check_resources_node)
        workflow.add_node("query_docs_only", self._query_docs_only_node)
        workflow.add_node("query_docs_and_resources", self._query_docs_and_resources_node)
        workflow.add_node("aggregate_results", self._aggregate_results_node)
        workflow.add_node("handle_error", self._handle_error_node)

        # 设置入口点
        workflow.set_entry_point("parse_intent")

        # 添加条件路由
        workflow.add_conditional_edges(
            "parse_intent",
            self._route_after_intent_parsing,
            {
                "check_resources": "check_resources",
                "error": "handle_error"
            }
        )

        workflow.add_conditional_edges(
            "check_resources",
            self._route_after_resource_check,
            {
                "docs_only": "query_docs_only",
                "docs_and_resources": "query_docs_and_resources"
            }
        )

        # 添加普通边
        workflow.add_edge("query_docs_only", "aggregate_results")
        workflow.add_edge("query_docs_and_resources", "aggregate_results")
        workflow.add_edge("aggregate_results", END)
        workflow.add_edge("handle_error", END)

        # 编译图（带检查点支持）
        return workflow.compile(checkpointer=self.memory)

    def _route_after_intent_parsing(self, state: AdvancedMixQueryState) -> Literal["check_resources", "error"]:
        """意图解析后的路由决策"""
        if state.get("error"):
            return "error"
        if not state.get("parsed_intents"):
            return "error"
        return "check_resources"

    def _route_after_resource_check(self, state: AdvancedMixQueryState) -> Literal["docs_only", "docs_and_resources"]:
        """资源检查后的路由决策"""
        return "docs_and_resources" if state.get("has_resources") else "docs_only"

    def _parse_intent_node(self, state: AdvancedMixQueryState) -> AdvancedMixQueryState:
        """意图解析节点"""
        try:
            logger.info(f"执行意图解析节点: {state['query']}")

            intent_parser = IntentParser()
            parsed_intents = intent_parser.parse(state["query"])

            if not parsed_intents:
                return {
                    **state,
                    "error": "无法解析用户意图",
                    "processing_stage": "intent_parsing_failed"
                }

            logger.info(f"解析到的意图: {parsed_intents}")

            return {
                **state,
                "parsed_intents": parsed_intents,
                "processing_stage": "intent_parsed"
            }
        except Exception as e:
            logger.error(f"意图解析节点失败: {e}")
            return {
                **state,
                "error": f"意图解析失败: {str(e)}",
                "processing_stage": "intent_parsing_error"
            }

    def _check_resources_node(self, state: AdvancedMixQueryState) -> AdvancedMixQueryState:
        """检查是否包含资源查询"""
        try:
            logger.info("检查资源查询需求")

            has_resources = any(
                intent.get('resources') and len(intent['resources']) > 0
                for intent in state["parsed_intents"]
            )

            logger.info(f"是否包含资源查询: {has_resources}")

            return {
                **state,
                "has_resources": has_resources,
                "processing_stage": "resource_checked"
            }
        except Exception as e:
            logger.error(f"资源检查失败: {e}")
            return {
                **state,
                "error": f"资源检查失败: {str(e)}",
                "processing_stage": "resource_check_error"
            }

    def _query_docs_only_node(self, state: AdvancedMixQueryState) -> AdvancedMixQueryState:
        """仅查询文档节点"""
        try:
            logger.info("执行仅文档查询")

            doc_results = []
            for intent in state["parsed_intents"]:
                result = self._query_knowledge_base(intent)
                if result:
                    doc_results.append({
                        'intent': intent,
                        'result': result
                    })

            logger.info(f"文档查询完成，结果数量: {len(doc_results)}")

            return {
                **state,
                "doc_results": doc_results,
                "processing_stage": "docs_queried"
            }
        except Exception as e:
            logger.error(f"文档查询失败: {e}")
            return {
                **state,
                "error": f"文档查询失败: {str(e)}",
                "processing_stage": "docs_query_error"
            }

    def _query_docs_and_resources_node(self, state: AdvancedMixQueryState) -> AdvancedMixQueryState:
        """并发查询文档和资源节点"""
        try:
            logger.info("执行并发文档和资源查询")

            parsed_intents = state["parsed_intents"]
            doc_results = []
            resource_results = []

            with ThreadPoolExecutor(max_workers=10) as executor:
                future_to_type = {}

                # 提交知识库查询任务
                for intent in parsed_intents:
                    future = executor.submit(self._query_knowledge_base, intent)
                    future_to_type[future] = ('doc', intent)

                    # 提交CMDB查询任务
                    if intent.get('resources'):
                        for resource in intent['resources']:
                            future = executor.submit(self._query_cmdb, resource)
                            future_to_type[future] = ('resource', resource)

                # 收集结果
                for future in as_completed(future_to_type):
                    query_type, query_info = future_to_type[future]
                    try:
                        result = future.result()
                        if query_type == 'doc':
                            if result:
                                doc_results.append({
                                    'intent': query_info,
                                    'result': result
                                })
                        else:  # resource
                            if result:
                                resource_results.append({
                                    'resource': query_info,
                                    'result': result
                                })
                    except Exception as e:
                        logger.error(f"查询失败 {query_type} - {query_info}: {e}")

            logger.info(f"并发查询完成，文档结果: {len(doc_results)}, 资源结果: {len(resource_results)}")

            return {
                **state,
                "doc_results": doc_results,
                "resource_results": resource_results,
                "processing_stage": "all_queried"
            }
        except Exception as e:
            logger.error(f"并发查询失败: {e}")
            return {
                **state,
                "error": f"并发查询失败: {str(e)}",
                "processing_stage": "concurrent_query_error"
            }

    def _aggregate_results_node(self, state: AdvancedMixQueryState) -> AdvancedMixQueryState:
        """结果聚合节点"""
        try:
            logger.info("执行结果聚合")

            if state.get("error"):
                return state

            aggregator = ResultAggregator()
            final_answer = aggregator.aggregate(
                original_query=state["query"],
                parsed_intents=state["parsed_intents"],
                doc_results=state["doc_results"],
                resource_results=state["resource_results"]
            )

            logger.info("结果聚合完成")

            return {
                **state,
                "final_answer": final_answer,
                "processing_stage": "completed"
            }
        except Exception as e:
            logger.error(f"结果聚合失败: {e}")
            return {
                **state,
                "error": f"结果聚合失败: {str(e)}",
                "processing_stage": "aggregation_error"
            }

    def _handle_error_node(self, state: AdvancedMixQueryState) -> AdvancedMixQueryState:
        """错误处理节点"""
        logger.error(f"进入错误处理节点: {state.get('error')}")

        return {
            **state,
            "final_answer": f"查询失败: {state.get('error', '未知错误')}",
            "processing_stage": "error_handled"
        }

    def _query_knowledge_base(self, intent: Dict[str, Any]) -> List[Document]:
        """查询知识库"""
        try:
            query_text = intent['intention']
            keywords = intent['keywords']
            return hybrid_search_optimized(query_text, self.collection_name, keywords)
        except Exception as e:
            logger.error(f"知识库查询失败: {e}")
            return []

    def _query_cmdb(self, resource: str) -> list[TextContent | ImageContent | EmbeddedResource] | None:
        """查询CMDB资源"""
        async def _async_query_cmdb():
            try:
                async with sse_client(url="https://tt-telemetry.ttyuyin.com/sse") as (read, write):
                    session = ClientSession(read, write)
                    async with session:
                        await session.initialize()
                        result = await session.call_tool("query_cmdb", {"query": resource})
                        logger.info(f"query_cmdb result={result.content}")
                        return result.content
            except Exception as e:
                logger.error(f"CMDB查询失败 {resource}: {e}")
                return None

        try:
            return asyncio.run(_async_query_cmdb())
        except Exception as e:
            logger.error(f"CMDB查询执行失败 {resource}: {e}")
            return None
