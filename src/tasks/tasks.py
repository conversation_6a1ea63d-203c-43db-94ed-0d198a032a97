import datetime
import logging
import os
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor, as_completed
from typing import IO, Iterator

import lark_oapi as lark
import tenacity
from langchain_core.vectorstores import VectorStore
from langchain_core.documents import Document
from langchain_milvus import Milvus
from lark_oapi.api.im.v1 import GetMessageResourceRequest, GetMessageResourceResponse

from settings import get_or_creat_settings_ins
from src.core.rag.doa import operator
from src.core.rag.doa.summary import create_document_summary
from src.helper.markdown_parser import parse_markdown_first_level
from src.core.rag.sources.feishu.lister.file import FileInfo
from src.core.rag.sources.feishu.lister.folder import FolderWalker
from src.core.rag.sources.feishu.lister.wiki import SpaceWalker
from src.helper.client import get_lark_client
from src.helper.client.milvus import create_collection, delete_by_source
from src.helper.document_filter.db import <PERSON><PERSON>ilter
from src.helper.document_filter.filter import <PERSON><PERSON><PERSON><PERSON>, Dummy<PERSON>ilter
from src.helper.document_loader import Lark<PERSON><PERSON>oa<PERSON>
from src.modules.llms import LLMFactory, EmbeddingType

logger = logging.getLogger(__name__)


@tenacity.retry(
    stop=tenacity.stop_after_attempt(3),
    wait=tenacity.wait_exponential(multiplier=2, max=10),
)
def download_resources_in_feishu(
        client: lark.Client,
        file: IO[bytes],
        message_id: str,
        file_key: str,
        file_type: str,
):
    # 构造请求对象
    request: GetMessageResourceRequest = (
        GetMessageResourceRequest.builder()
        .message_id(message_id)
        .file_key(file_key)
        .type(file_type)
        .build()
    )
    # 发起请求
    assert client.im is not None
    response: GetMessageResourceResponse = client.im.v1.message_resource.get(request)

    # 处理失败返回
    if not response.success():
        raise Exception(
            f"client.im.v1.message_resource.get failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
        )
    if response.file is None:
        raise Exception("file is None")

    file.write(response.file.read())
    file.flush()


def lark_resource2vector_worker(
        client: lark.Client, info: FileInfo, vector: VectorStore
):
    try:
        loader = LarkMDLoader(
            client=client,
            source=info.source,
            document_id=info.token,
            title=info.name,
            doc_type=info.type,
        )
        docs = loader.load()
        batch = 100
        for start_index in range(0, len(docs), batch):
            batch_docs = docs[start_index:start_index + batch]
            filtered_batch_docs = [doc for doc in batch_docs if len(doc.page_content) > 10]
            for doc in filtered_batch_docs:
                keywords, summary = create_document_summary(doc.metadata['title'], doc.page_content)
                doc.metadata['content'] = doc.page_content
                doc.metadata['token'] = info.token
                doc.metadata['keywords'] = keywords
                doc.page_content = summary
            if len(filtered_batch_docs) == 0:
                continue
            vector.add_documents(batch_docs)
    except Exception as e:
        logger.error(f"lark_resource2vector_worker error: {e}, filieInof: {info}")
        return "", 0, ""
    return info.token, info.update_time, info.source


def lark_resource2vector_paragraph_worker(
        client: lark.Client, info: FileInfo, vector: VectorStore
):
    """
    处理文档段落级别的向量存储
    使用 parse_markdown_first_level 将文档按第一级标题拆分，
    为每个段落生成摘要并存储到向量数据库
    """
    try:
        loader = LarkMDLoader(
            client=client,
            source=info.source,
            document_id=info.token,
            title=info.name,
            doc_type=info.type,
        )
        docs = loader.load()

        # 处理每个文档
        paragraph_docs = []
        for doc in docs:
            if len(doc.page_content) <= 10:
                continue

            # 使用 parse_markdown_first_level 解析文档
            markdown_nodes = parse_markdown_first_level(doc.page_content)

            # 为每个第一级标题段落创建新的文档
            for node in markdown_nodes:
                if len(node.content.strip()) <= 10:
                    continue

                # 为段落生成摘要
                keywords, paragraph_summary = create_document_summary(doc.metadata['title'] + "\n" + node.title,
                                                                      node.content)
                # 创建新的文档对象，保持原文档的基本信息
                paragraph_doc = Document(
                    page_content=paragraph_summary,
                    metadata={
                        'title': doc.metadata['title'],  # 使用原文档标题
                        'content': node.content,  # 段落完整内容
                        'token': info.token,  # 使用原文档token
                        'source': info.source,  # 使用原文档source
                        'paragraph_title': node.title,  # 段落标题
                        'keywords': keywords,
                    }
                )
                paragraph_docs.append(paragraph_doc)

        # 批量添加到向量数据库
        if paragraph_docs:
            batch = 100
            for start_index in range(0, len(paragraph_docs), batch):
                batch_docs = paragraph_docs[start_index:start_index + batch]
                vector.add_documents(batch_docs)

    except Exception as e:
        logger.error(f"lark_resource2vector_paragraph_worker error: {e}, fileInfo: {info}")
        return "", 0, ""
    return info.token, info.update_time, info.source


def update_knowledge_base():
    start = datetime.datetime.now()
    config = get_or_creat_settings_ins()
    logger.info(f"start walk space {datetime.datetime.now()}")
    embeddings = LLMFactory(config.embedding).BuildEmbedding(EmbeddingType.HUGGINGFACE)
    client = get_lark_client()
    filter: DocFilter
    if config.knowledge_base.use_cache:
        filter = DBFilter()
    else:
        filter = DummyFilter()

    dbs = operator.get_all_common_knowledge_base()
    if len(dbs) == 0:
        logger.info("no knowledge base")
        return

    for db in dbs:
        logger.info(f"start walk space {db.query_index}")
        create_collection(collection_name=db.query_index)
        vector = Milvus(
            collection_name=db.query_index,
            embedding_function=embeddings,
            connection_args={
                "uri": config.milvus.uri,
                "token": f"{config.milvus.user}:{os.getenv('MILVUS__PASSWORD')}",
                "db_name": config.milvus.db_name,
                "timeout": config.milvus.timeout
            },
            auto_id=True,
        )

        # 创建段落级别的集合
        paragraph_collection_name = db.query_index + "_paragraph"
        logger.info(f"start walk space for paragraph collection {paragraph_collection_name}")
        create_collection(collection_name=paragraph_collection_name)
        paragraph_vector = Milvus(
            collection_name=paragraph_collection_name,
            embedding_function=embeddings,
            connection_args={
                "uri": config.milvus.uri,
                "token": f"{config.milvus.user}:{os.getenv('MILVUS__PASSWORD')}",
                "db_name": config.milvus.db_name,
                "timeout": config.milvus.timeout
            },
            auto_id=True,
        )
        walk: Iterator[FileInfo]
        if db.source_type == "wiki":
            walk = SpaceWalker(
                client=client,
                space_id=db.space_id,
                start_folder_token=db.dir_token,
                wiki_prefix=db.space_prefix,
                ignore=config.knowledge_base.ignore_docs,
            )
        else:
            walk = FolderWalker(client, db.dir_token)

        executor = ThreadPoolExecutor(max_workers=10)
        task_list = []
        paragraph_task_list = []
        space_walker_list = []

        for d in walk:
            assert db.id is not None
            space_walker_list.append(d.source)
            update = filter.is_update(db.id, d.token, d.update_time)
            if update is False:
                logger.info(f"no update skip {d.source}")
                continue
            if update is True:
                delete_by_source(collection_name=db.query_index, source=d.source)
                logger.info(f"delete {d.source}")

            task_list.append(
                executor.submit(lark_resource2vector_worker, client, d, vector)
            )

            paragraph_task_list.append(
                executor.submit(lark_resource2vector_paragraph_worker, client, d, paragraph_vector)
            )
        id = db.id
        assert id is not None

        # 处理原有的文档级别任务
        for t in as_completed(task_list):
            token, update_time, source = t.result()
            if token != "":
                filter.add(id, token, update_time, source)

        # 处理段落级别任务
        for t in as_completed(paragraph_task_list):
            token, update_time, source = t.result()
            if token != "":
                logger.info(f"Paragraph processing completed for {source}")

        executor.shutdown()

        # delete docs
        all_file_info = operator.get_parent_all_file_info(id)
        file_info_list = [i.source for i in all_file_info]
        delete_list = [item for item in file_info_list if item not in space_walker_list]
        for delete_source in delete_list:
            delete_by_source(db.query_index, delete_source)
            filter.delete(id, delete_source)

    end = datetime.datetime.now()
    logger.info(f"start walk space {start} end walk space {end} cost {end - start}")


if __name__ == "__main__":
    update_knowledge_base()
