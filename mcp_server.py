from dotenv import load_dotenv
from src.log import logger
from mcp.server import Server
from mcp.types import Tool, TextContent

from src.mcp.tools import VectorTookit

load_dotenv(".env")

app = Server("knowledge_mcp_server")


@app.list_tools()
async def list_tools() -> list[Tool]:
    """List tools."""
    result = []
    try:
        tools = []
        tools.extend(VectorTookit().get_tools())
        for tool in tools:
            input_schema = tool.get_input_schema().model_json_schema()
            result.append(Tool(
                name=tool.name,
                description=tool.description,
                inputSchema={
                    "type": "object",
                    "properties": input_schema['properties'],
                }
            ))
    except Exception as e:
        logger.error(f"Failed to list tools: {e}")
    return result


@app.call_tool()
async def call_tool(name: str, arguments: dict) -> list[TextContent]:
    """call_tool"""
    logger.info(f"Calling tool: {name} with arguments: {arguments}")
    tools = []
    tools.extend(VectorTookit().get_tools())
    tool = next((t for t in tools if t.name == name), None)
    if not tool:
        raise ValueError(f"Tool {name} not found")
    try:
        result = tool.run(arguments)
        return [TextContent(type="text", text=str(result))]
    except Exception as e:
        logger.error(f"Failed to call tool '{name}': {e}")
        return [TextContent(type="text", text=f"工具执行失败: {str(e)}")]


def main():
    """Run the server using SSE transport."""
    from mcp.server.sse import SseServerTransport
    from starlette.applications import Starlette
    from starlette.routing import Mount, Route

    sse = SseServerTransport("/messages/")

    async def handle_sse(request):
        async with sse.connect_sse(
                request.scope, request.receive, request._send
        ) as streams:
            await app.run(
                streams[0],
                streams[1],
                app.create_initialization_options(),
            )

    starlette_app = Starlette(
        debug=True,
        routes=[
            Route("/sse", endpoint=handle_sse),
            Mount("/messages/", app=sse.handle_post_message),
        ],
    )

    import uvicorn
    uvicorn.run(starlette_app, host="0.0.0.0", port=8001)


if __name__ == "__main__":
    main()
