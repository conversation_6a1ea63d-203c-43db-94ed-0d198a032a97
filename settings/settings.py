from typing import Optional

from pydantic import BaseModel, Field

from settings.common import CommonSettings


class LarkClient(BaseModel):
    app_id: str = Field(..., description="应用ID")
    app_secret: str = Field(..., description="应用密钥")
    encrypt_key: str = Field(..., description="加密密钥")
    verifivation_token: str = Field(..., description="验证令牌")
    name: str = Field(..., description="应用名称")


class PostgresSettings(BaseModel):
    user: str = Field(..., description="数据库用户名")
    password: str = Field(..., description="数据库密码")
    host: str = Field(..., description="数据库主机")
    port: int = Field(5432, description="数据库端口")
    database: str = Field(..., description="数据库名")
    min_size: int = Field(1, description="连接池最小连接数")
    max_size: int = Field(10, description="连接池最大连接数")


class MilvusSettings(BaseModel):
    uri: str = Field(..., description="Milvus服务器主机地址")
    user: Optional[str] = Field(None, description="用户名")
    db_name: Optional[str] = Field(None, description="数据库名")
    timeout: int = Field(20, description="连接超时时间（秒）")


class EmbeddingSettings(BaseModel):
    model: str = Field(..., description="模型")


class KnowledgeBaseSettings(BaseModel):
    index: str = Field(..., description="milvus index")
    ignore_docs: str = Field(..., description="ignore docs")
    use_cache: bool = Field(..., description="use cache")


class ReRankSettings(BaseModel):
    url: str = Field(..., description="rerank url")


class ModelSettings(BaseModel):
    default: str = Field(..., description="默认模型")
    doc_llm: str = Field(..., description="doc_llm")


class Settings(CommonSettings):
    lark: LarkClient = Field(..., description="lark settings")
    postgres: PostgresSettings = Field(..., description="postgres settings")
    milvus: MilvusSettings = Field(..., description="milvus settings")
    embedding: EmbeddingSettings = Field(..., description="embedding settings")
    knowledge_base: KnowledgeBaseSettings = Field(..., description="knowledge base settings")
    models: ModelSettings = Field(..., description="模型配置")
    rerank: ReRankSettings = Field(..., description="rerank settings")
