import os
import sys
from pathlib import Path
from typing import Any

from pydantic_settings import BaseSettings, InitSettingsSource
from pydantic_settings.sources import ConfigFileSourceMixin, PathType

from src.schema import RUNTIME_ENV


class TomlConfigByEnvSettingsSource(InitSettingsSource, ConfigFileSourceMixin):
    """
    A source class that loads variables from a TOML file
    """

    def __init__(
            self,
            settings_cls: type[BaseSettings],
            toml_dir: PathType | None = None,
    ):
        env = os.environ.get(RUNTIME_ENV, "noprod")
        path = Path(toml_dir)  # type: ignore
        config = {}
        config.update(self._read_file(path.joinpath("common.toml")))
        config.update(self._read_file(path.joinpath(f"{env}.toml")))
        config.update(self._read_file(path.joinpath(f"local.toml")))
        super().__init__(settings_cls, config)

    def _read_file(self, file_path: Path) -> dict[str, Any]:
        if not file_path.exists():
            return {}
        with open(file_path, mode="rb") as toml_file:
            if sys.version_info < (3, 11):
                import tomli

                return tomli.load(toml_file)
            import tomllib

            return tomllib.load(toml_file)
